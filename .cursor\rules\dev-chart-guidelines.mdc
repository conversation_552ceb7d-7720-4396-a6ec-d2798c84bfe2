---
description: AI视频创作工具系统图表集合 - 包含所有系统架构图、业务流程图和技术架构图 
globs:
alwaysApply: true
---

# AI视频创作工具系统图表集合

## 📊 系统架构图表

### 📊 完整系统架构图（环境切换优化版）

```mermaid
graph TB
    subgraph "用户层 - 视频创作工具"
        A[Py视频创作工具<br/>完整创作功能<br/>客户端视频编辑]
        B[WEB网页工具<br/>✅展示职责：首页工具展示、功能介绍、价格方案<br/>✅用户中心：注册登录、充值积分、积分明细、代理推广、代理结算<br/>✅作品广场：作品展示浏览、分类筛选、搜索查看、作品详情展示<br/>✅响应式设计：PC端、移动端、Py视频创作工具嵌入<br/>❌禁止：视频创作、AI生成、WebSocket通信、作品发布创建]
        M[管理后台<br/>@php/backend/<br/>基于Laravel 10<br/>✅系统配置管理：AI平台配置、系统参数设置<br/>✅用户管理：用户信息、权限管理、积分管理<br/>✅数据统计：业务数据分析、系统监控<br/>✅内容审核：作品审核、资源管理]
    end

    subgraph "业务服务层"
        C[工具API接口服务<br/>@php/api/<br/>基于Lumen 10.x<br/>统一API接口<br/>🚨环境切换机制实现层]
        C1[WebSocket服务<br/>swoole-cli artisan websocket:serve<br/>仅为Py视频创作工具提供实时通信]
        C2[AI资源管理服务<br/>资源生成+版本控制+本地导出<br/>可选：作品发布+审核系统]
    end

    subgraph "环境切换服务客户端层"
        SC1[AiServiceClient<br/>AI服务环境切换<br/>mock/real模式自动切换]
        SC2[ThirdPartyServiceClient<br/>第三方服务环境切换<br/>mock/real模式自动切换]
    end

    subgraph "开发支持层（模拟服务）"
        D[AI服务模拟<br/>@php/aiapi/<br/>本地开发模拟真实AI平台<br/>仅负责模拟，不包含环境切换]
        D2[第三方服务模拟<br/>@php/thirdapi/<br/>模拟微信、支付宝等<br/>仅负责模拟，不包含环境切换]
    end

    subgraph "数据存储层"
        E[MySQL数据库<br/>ai_tool<br/>主存储+事务保证<br/>+AI资源表+版本表+作品广场表]
        F[Redis缓存<br/>WebSocket会话管理<br/>快速查询+状态同步<br/>+资源状态缓存]
    end

    subgraph "真实AI服务（生产环境）"
        G[DeepSeek API<br/>剧情生成]
        H[LiblibAI API<br/>图像生成]
        I[KlingAI API<br/>视频生成]
        J[MiniMax API<br/>语音处理]
        K[火山引擎豆包 API<br/>专业语音AI]
    end

    subgraph "真实第三方服务（生产环境）"
        TP1[微信 API<br/>OAuth认证]
        TP2[支付宝 API<br/>支付服务]
        TP3[短信服务 API<br/>验证码发送]
    end

    %% HTTP API 连接 (蓝色虚线) - 三个工具都使用
    A -.->|🔵 HTTP API<br/>创作功能调用| C
    B -.->|🔵 HTTP API<br/>展示功能+用户中心功能+作品广场功能<br/>❌禁用WebSocket| C
    M -.->|🔵 HTTP API<br/>管理功能调用<br/>AdminApi控制器| C

    %% WebSocket 连接 (绿色粗线) - 仅Py视频创作工具使用
    A ==>|🟢 WebSocket实时通信<br/>AI生成进度推送<br/>任务状态通知| C1

    %% 服务间调用 (红色线) - 避免循环依赖
    C -->|🔴 业务逻辑调用<br/>通过服务客户端| SC1
    C -->|🔴 业务逻辑调用<br/>通过服务客户端| SC2
    C -->|🔴 资源管理调用<br/>版本控制+审核| C2
    C1 -->|🔴 获取API密钥<br/>安全传输| C
    C2 -->|🔴 AI生成调用<br/>资源创建| SC1

    %% 环境切换调用 (紫色线) - 核心机制
    SC1 -->|🟣 开发环境<br/>mock模式| D
    SC1 -->|🟣 生产环境<br/>real模式| G
    SC1 -->|🟣 生产环境<br/>real模式| H
    SC1 -->|🟣 生产环境<br/>real模式| I
    SC1 -->|🟣 生产环境<br/>real模式| J
    SC1 -->|🟣 生产环境<br/>real模式| K
    SC2 -->|🟣 开发环境<br/>mock模式| D2
    SC2 -->|🟣 生产环境<br/>real模式| TP1
    SC2 -->|🟣 生产环境<br/>real模式| TP2
    SC2 -->|🟣 生产环境<br/>real模式| TP3

    %% 数据库连接 (橙色线) - 双重保障
    C -->|🟠 数据存储<br/>事务保证| E
    C -->|🟠 缓存操作<br/>快速查询| F
    C1 -->|🟠 会话管理<br/>状态同步| F
    C2 -->|🟠 资源数据存储<br/>版本管理| E
    C2 -->|🟠 资源状态缓存<br/>快速查询| F

    %% 节点样式 - 高对比度清晰字体
    classDef userLayer fill:#FFFFFF,stroke:#1976D2,stroke-width:3px,color:#000000
    classDef adminLayer fill:#FFFFFF,stroke:#FF9800,stroke-width:3px,color:#000000
    classDef serviceLayer fill:#FFFFFF,stroke:#7B1FA2,stroke-width:3px,color:#000000
    classDef clientLayer fill:#FFFFFF,stroke:#E91E63,stroke-width:3px,color:#000000
    classDef mockLayer fill:#FFFFFF,stroke:#4CAF50,stroke-width:3px,color:#000000
    classDef dataLayer fill:#FFFFFF,stroke:#F57C00,stroke-width:3px,color:#000000
    classDef realAiLayer fill:#FFFFFF,stroke:#388E3C,stroke-width:3px,color:#000000
    classDef realThirdLayer fill:#FFFFFF,stroke:#795548,stroke-width:3px,color:#000000

    class A,B userLayer
    class M adminLayer
    class C,C1,C2 serviceLayer
    class SC1,SC2 clientLayer
    class D,D2 mockLayer
    class E,F dataLayer
    class G,H,I,J,K realAiLayer
    class TP1,TP2,TP3 realThirdLayer
```

### 📊 AI服务集成模拟机制架构图

```mermaid
graph TB
    subgraph "本地开发环境"
        A[Py视频创作工具] --> B[工具API接口服务]
        B --> E[AI服务集成模拟返回数据服务]
        E -.->|仅模拟，不真实调用| F1[DeepSeek API格式模拟<br/>剧情生成/角色生成]
        E -.->|仅模拟，不真实调用| F2[LiblibAI API格式模拟<br/>图像生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F3[KlingAI API格式模拟<br/>图像生成/视频生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F4[MiniMax API格式模拟<br/>全业务支持]
        E -.->|仅模拟，不真实调用| F5[火山引擎豆包 API格式模拟<br/>语音合成/音效生成/音色生成]

        B --> T[第三方服务集成模拟返回数据服务]
        T -.->|仅模拟，不真实调用| G1[微信服务API格式模拟<br/>OAuth登录/微信支付]
        T -.->|仅模拟，不真实调用| G2[支付宝API格式模拟<br/>统一收单/退款查询]
        T -.->|仅模拟，不真实调用| G3[短信服务API格式模拟<br/>阿里云/腾讯云短信]
        T -.->|仅模拟，不真实调用| G4[邮件服务API格式模拟<br/>SMTP/SendCloud]
    end

    subgraph "生产环境"
        A2[Py视频创作工具] --> B2[工具API接口服务]
        B2 --> F6[真实第三方AI平台<br/>DeepSeek/LiblibAI/KlingAI<br/>MiniMax/火山引擎豆包]
        B2 --> G5[真实第三方服务平台<br/>微信/支付宝/阿里云/腾讯云]
    end

    style E fill:#fce4ec,stroke:#e91e63
    style T fill:#fff8e1,stroke:#ff9800
    style F1 fill:#ffebee
    style F2 fill:#ffebee
    style F3 fill:#ffebee
    style F4 fill:#ffebee
    style F5 fill:#ffebee
    style G1 fill:#ffebee
    style G2 fill:#ffebee
    style G3 fill:#ffebee
    style G4 fill:#ffebee
    style F6 fill:#e8f5e8,stroke:#4caf50
    style G5 fill:#e8f5e8,stroke:#4caf50
```

### 📊 AI服务调用流程对比图

```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant API as 工具API接口服务
    participant Mock as AI模拟服务
    participant Real as 真实AI平台

    Note over P,Mock: 本地开发阶段
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Mock: 真实调用AI平台格式接口<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Note over Mock: ✅ 唯一模拟职责<br/>1. 按对应AI平台要求验证参数<br/>2. 模拟对应平台响应状态
    alt 参数验证失败
        Mock->>API: 返回对应AI平台格式参数错误
    else 参数验证通过
        Mock->>API: 模拟成功/失败/超时状态
    end
    API->>P: 透明传递模拟结果

    Note over P,Real: 生产环境
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Real: 真实调用对应AI平台<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Real->>API: 返回真实结果
    API->>P: 透明传递真实结果
```

### 📊 项目依赖关系图

```mermaid
graph TB
    subgraph "本地开发环境"
        A[Py视频创作工具] --> B[工具API接口服务]
        C[WEB网页工具] --> B
        D[管理后台] --> B
        B --> E[AI服务集成模拟返回数据服务]
        B --> T[第三方服务集成模拟返回数据服务]
        E -.->|仅模拟，不真实调用| F1[AI平台API格式模拟]
        T -.->|仅模拟，不真实调用| G1[第三方服务API格式模拟]
    end

    subgraph "生产环境"
        A2[Py视频创作工具] --> B2[工具API接口服务]
        C2[WEB网页工具] --> B2
        D2[管理后台] --> B2
        B2 --> F2[真实第三方AI平台]
        B2 --> G2[真实第三方服务平台]
    end

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style T fill:#fff8e1
    style F1 fill:#ffebee
    style G1 fill:#ffebee
    style F2 fill:#e8f5e8
    style G2 fill:#e8f5e8
```

#### 📋 依赖关系说明

**依赖说明**：
- **工具API接口服务**: 依赖"AI服务集成模拟返回数据服务"和"第三方服务集成模拟返回数据服务"提供API接口支持本地开发
- **Py视频创作工具**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发
- **WEB网页工具**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发
- **管理后台**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发

**环境切换机制**：
- **本地开发环境**: 通过模拟服务提供完整的开发支持，无需真实第三方平台
- **生产环境**: 直接连接真实的AI平台和第三方服务，提供完整的生产功能
- **切换方式**: 通过配置文件控制，开发和生产环境无缝切换

## 🔄 Py视频创作工具业务流程图

### 🔄 用户管理业务流程

#### Py视频创作工具业务流程A-1: 用户注册流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方服务

    P->>A: 用户注册请求(用户名/邮箱/密码)
    A->>DB: 检查用户名/邮箱是否存在
    alt 用户已存在
        DB->>A: 返回用户已存在
        A->>P: 返回注册失败(用户已存在)
    else 用户不存在
        A->>A: 密码加密处理
        A->>DB: 创建新用户记录(状态:待验证)
        A->>TP: 发送验证邮件/短信
        TP->>A: 返回发送结果
        A->>R: 缓存验证码(5分钟过期)
        A->>P: 返回注册成功(待验证)

        Note over P: 用户输入验证码
        P->>A: 验证请求(验证码)
        A->>R: 验证验证码有效性
        alt 验证码有效
            A->>DB: 激活用户账户(状态:正常)
            A->>R: 清除验证码缓存
            A->>P: 返回验证成功
        else 验证码无效或过期
            A->>P: 返回验证失败(重新发送)
        end
    end
```

#### Py视频创作工具业务流程A-2: 用户登录流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 用户登录请求(用户名/密码)
    A->>DB: 查询用户信息
    alt 用户不存在
        A->>P: 返回登录失败(用户不存在)
    else 用户存在
        A->>A: 验证密码正确性
        alt 密码错误
            A->>DB: 记录登录失败次数
            A->>P: 返回登录失败(密码错误)
        else 密码正确
            A->>DB: 检查用户状态
            alt 用户被禁用
                A->>P: 返回登录失败(账户被禁用)
            else 用户状态正常
                A->>A: 生成自定义 Token
                A->>R: 存储Token(24小时过期)
                A->>DB: 更新最后登录时间
                A->>DB: 清除登录失败次数
                A->>P: 返回登录成功(Token+用户信息)
            end
        end
    end
```

#### Py视频创作工具业务流程A-3: Token验证流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant R as Redis缓存
    participant DB as MySQL数据库

    P->>A: API请求(携带Token)
    A->>A: 解析自定义 Token
    alt Token格式无效
        A->>P: 返回认证失败(Token无效)
    else Token格式有效
        A->>R: 检查Token是否在黑名单
        alt Token在黑名单
            A->>P: 返回认证失败(Token已失效)
        else Token有效
            A->>A: 验证Token签名和过期时间
            alt Token过期或签名无效
                A->>P: 返回认证失败(Token过期)
            else Token验证通过
                A->>DB: 查询用户当前状态
                alt 用户被禁用
                    A->>R: 将Token加入黑名单
                    A->>P: 返回认证失败(账户被禁用)
                else 用户状态正常
                    A->>A: 继续处理业务请求
                    Note over A: 执行具体的业务逻辑
                end
            end
        end
    end
```

#### Py视频创作工具业务流程A-4: 密码修改流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方服务

    Note over P: 用户已登录状态
    P->>A: 密码修改请求(旧密码/新密码/Token)
    A->>A: 验证Token有效性
    A->>DB: 查询用户信息
    A->>A: 验证旧密码正确性
    alt 旧密码错误
        A->>P: 返回修改失败(旧密码错误)
    else 旧密码正确
        A->>A: 验证新密码强度
        alt 新密码强度不足
            A->>P: 返回修改失败(密码强度不足)
        else 新密码符合要求
            A->>A: 加密新密码
            A->>DB: 更新用户密码
            A->>R: 将当前用户所有Token加入黑名单
            A->>TP: 发送密码修改通知邮件
            A->>P: 返回修改成功(需重新登录)
        end
    end
```

#### Py视频创作工具业务流程A-5: 忘记密码重置流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方服务

    P->>A: 忘记密码请求(邮箱)
    A->>DB: 查询邮箱对应用户
    alt 邮箱不存在
        A->>P: 返回邮箱不存在
    else 邮箱存在
        A->>A: 生成重置Token
        A->>R: 缓存重置Token(30分钟过期)
        A->>TP: 发送重置密码邮件(含重置链接)
        A->>P: 返回邮件发送成功

        Note over P: 用户点击邮件中的重置链接
        P->>A: 密码重置请求(重置Token/新密码)
        A->>R: 验证重置Token有效性
        alt Token无效或过期
            A->>P: 返回重置失败(链接已过期)
        else Token有效
            A->>A: 验证新密码强度
            A->>A: 加密新密码
            A->>DB: 更新用户密码
            A->>R: 清除重置Token
            A->>R: 将用户所有Token加入黑名单
            A->>TP: 发送密码重置成功通知
            A->>P: 返回重置成功(需重新登录)
        end
    end
```

### 🔄 业务功能流程

#### Py视频创作工具业务流程B-1: 充值积分流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方支付

    P->>A: 发起充值请求(金额/支付方式/Token)
    A->>A: 验证Token和充值参数
    A->>DB: 创建充值订单记录
    A->>TP: 调用支付接口(微信/支付宝)
    TP->>A: 返回支付二维码/链接
    A->>P: 返回支付信息

    Note over P: 用户完成支付
    TP->>A: 支付成功回调通知
    A->>A: 验证支付回调签名
    A->>DB: 查询订单状态
    alt 订单已处理
        A->>TP: 返回重复通知
    else 订单未处理
        A->>DB: 更新订单状态为已支付
        A->>DB: 增加用户积分余额
        A->>DB: 创建积分明细记录
        A->>R: 更新用户积分缓存
        A->>P: 推送充值成功通知
        A->>TP: 返回处理成功
    end
```

#### Py视频创作工具业务流程B-2: 积分管理流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 查询积分信息(Token)
    A->>A: 验证Token有效性
    A->>R: 检查积分缓存
    alt 缓存命中
        R->>A: 返回缓存的积分信息
    else 缓存未命中
        A->>DB: 查询用户积分余额
        A->>DB: 查询积分明细记录
        A->>R: 更新积分缓存
    end
    A->>P: 返回积分信息(余额/明细)

    Note over P: 用户请求积分明细
    P->>A: 查询积分明细(分页参数/Token)
    A->>DB: 分页查询积分明细
    A->>P: 返回积分明细列表(收入/支出/余额变化)
```

#### Py视频创作工具业务流程B-3: 代理推广流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 申请成为代理(Token)
    A->>A: 验证Token和用户资格
    A->>DB: 检查用户状态和历史记录
    alt 用户不符合代理条件
        A->>P: 返回申请失败(原因说明)
    else 用户符合条件
        A->>A: 生成唯一推广码
        A->>DB: 创建代理记录
        A->>R: 缓存代理信息
        A->>P: 返回代理申请成功(推广码)
    end

    Note over P: 查询推广数据
    P->>A: 查询推广统计(Token)
    A->>DB: 查询推广用户数量
    A->>DB: 查询推广收益统计
    A->>P: 返回推广数据(用户数/收益/转化率)

    Note over P: 新用户通过推广码注册
    P->>A: 用户注册(推广码)
    A->>DB: 验证推广码有效性
    A->>DB: 创建用户并绑定推广关系
    A->>DB: 记录推广奖励
    A->>R: 更新代理统计缓存
```

#### Py视频创作工具业务流程B-4: 代理结算流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方支付

    P->>A: 查询可结算金额(Token)
    A->>A: 验证代理身份
    A->>DB: 计算未结算推广收益
    A->>P: 返回可结算金额

    P->>A: 申请提现结算(金额/收款方式/Token)
    A->>A: 验证提现参数和最小金额
    A->>DB: 检查可结算余额
    alt 余额不足或不满足提现条件
        A->>P: 返回提现失败(原因)
    else 满足提现条件
        A->>DB: 创建提现申请记录
        A->>DB: 冻结对应金额
        A->>P: 返回提现申请成功(审核中)

        Note over A: 管理员审核通过后
        A->>TP: 调用支付接口转账
        TP->>A: 返回转账结果
        alt 转账成功
            A->>DB: 更新提现状态为成功
            A->>DB: 扣除已结算金额
            A->>R: 更新代理收益缓存
            A->>P: 推送提现成功通知
        else 转账失败
            A->>DB: 更新提现状态为失败
            A->>DB: 解冻冻结金额
            A->>P: 推送提现失败通知
        end
    end
```

#### Py视频创作工具业务流程B-5: 数据处理流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant FS as 文件存储

    P->>A: 上传项目数据(文件/参数/Token)
    A->>A: 验证Token和文件格式
    A->>A: 验证文件大小和类型
    alt 文件不符合要求
        A->>P: 返回上传失败(格式/大小错误)
    else 文件符合要求
        A->>FS: 存储文件到临时目录
        A->>A: 解析和验证数据内容
        A->>DB: 保存数据处理记录
        A->>R: 缓存处理状态
        A->>P: 返回上传成功(数据ID)

        Note over A: 异步处理数据
        A->>A: 数据清洗和格式化
        A->>DB: 更新处理进度
        A->>R: 更新缓存状态

        alt 数据处理成功
            A->>DB: 保存处理结果
            A->>FS: 移动文件到正式目录
            A->>P: 推送处理完成通知
        else 数据处理失败
            A->>DB: 记录错误信息
            A->>FS: 清理临时文件
            A->>P: 推送处理失败通知
        end
    end
```

### 🔄 项目管理流程

#### Py视频创作工具业务流程C-0: 视频创作项目创建流程（纯文本数据处理版）
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as Py视频创作工具前端
    participant A as 工具API接口服务
    participant C1 as WebSocket服务
    participant AI as 文生文AI服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    Note over A: ⚠️ 重要：本流程仅处理文本数据，严禁储存或中转任何资源文件

    Note over U: 🎬 用户进入项目创建页面

    %% 用户认证和权限检查
    U->>F: 访问视频创作页面
    F->>A: GET /py-api/auth/verify (Token验证)
    A->>R: 检查Token缓存状态
    A->>DB: 验证用户权限和配额
    A->>F: 返回用户信息和权限状态

    alt Token无效或过期
        F->>U: 跳转到登录页面
        U->>A: 用户登录请求(用户名/密码)
        A->>DB: 查询用户信息
        alt 用户不存在
            A->>F: 返回登录失败(用户不存在)
        else 用户存在
            A->>A: 验证密码正确性
            alt 密码错误
                A->>DB: 记录登录失败次数
                A->>F: 返回登录失败(密码错误)
            else 密码正确
                A->>DB: 检查用户状态
                alt 用户被禁用
                    A->>F: 返回登录失败(账户被禁用)
                else 用户状态正常
                    A->>A: 生成自定义 Token
                    A->>R: 存储Token(24小时过期)
                    A->>DB: 更新最后登录时间
                    A->>DB: 清除登录失败次数
                    A->>F: 返回登录成功(Token+用户信息)
                end
            end
        end
    end

    Note over F: 🎨 项目创建界面初始化

    %% 加载风格模板列表
    F->>A: GET /py-api/styles/templates?type=video
    A->>DB: 查询p_style_library表风格模板
    A->>R: 检查风格模板缓存
    DB->>A: 返回风格模板列表(卡通可爱/写实风格/科幻风格/古风/现代等)
    A->>F: 返回风格选项数据
    F->>U: 渲染左侧风格图片列表 + 右侧内容输入区域

    Note over U: 👤 用户交互：风格选择和内容输入

    %% 用户选择风格
    U->>F: 选择风格模板(style_id: 1, 卡通可爱风格)
    F->>F: 动态更新右侧区域<br/>显示内容类型选择器

    %% 用户选择内容类型并输入
    U->>F: 选择内容类型 + 在多行输入框录入内容

    alt 选择"自有故事剧情分镜"(own_story)
        U->>F: 录入完整故事内容<br/>"小猫咪在森林中寻找宝藏的冒险故事..."
        Note over F: 📝 自有故事分镜处理模式
    else 选择"AI故事分镜"(ai_story)
        U->>F: 录入提示词或故事大纲<br/>"创作一个关于小猫咪冒险的儿童故事"
        Note over F: 🤖 AI故事生成+分镜处理模式
    end

    Note over F: 🚀 提交项目创建请求

    %% 用户体验优化：提交时的界面反馈
    U->>F: 点击提交按钮
    F->>F: 界面保持不变 + 显示处理罩层<br/>显示实时进度条和状态文字<br/>禁用提交按钮，防止重复提交

    %% 建立WebSocket连接用于实时进度推送
    F->>C1: 建立WebSocket连接<br/>订阅频道: project_progress_{user_id}
    C1->>F: 连接确认，准备接收进度推送

    %% 提交项目创建请求
    F->>A: POST /py-api/projects/create-with-story<br/>{style_id: 1, content_type: "own_story", story_content: "...", token}
    A->>A: 验证请求参数和用户权限
    A->>DB: 检查用户项目配额(p_users表)
    A->>DB: 检查用户积分余额(p_points表)

    alt 权限验证失败或配额不足
        A->>F: 返回错误信息(code: 1006积分不足/400配额超限)
        F->>F: 隐藏处理罩层
        F->>U: 显示升级提示或充值引导
    else 权限验证通过
        %% 创建项目记录（仅数据库操作）
        A->>DB: INSERT INTO p_projects<br/>(user_id, style_id, content_type, status='processing', created_at)
        DB->>A: 返回项目ID(project_id)

        Note over A: 📝 注意：此阶段不创建任何文件或文件夹

        Note over A: 🤖 AI文本处理阶段（仅处理文本数据，不产生资源文件）

        %% 进度推送：开始处理
        A->>C1: 推送进度更新<br/>{project_id, progress: 10%, status: "初始化AI文本处理"}
        C1->>F: WebSocket实时推送进度
        F->>U: 更新进度条：10% "初始化AI文本处理"

        %% AI文本处理分支（项目创建阶段的简化AI处理）
        alt 自有故事剧情分镜处理
            A->>C1: 推送进度<br/>{progress: 25%, status: "分析故事文本结构"}
            C1->>F: 实时推送进度
            F->>U: 更新进度条：25% "分析故事文本结构"

            A->>A: 调用内置AI文本处理<br/>生成项目标题和基础分镜文本
            Note over A: 项目创建阶段使用简化的AI处理<br/>主要用于生成标题，复杂AI任务在C-1流程中进行

            A->>C1: 推送进度<br/>{progress: 60%, status: "生成分镜文本描述"}
            C1->>F: 实时推送进度
            F->>U: 更新进度条：60% "生成分镜文本描述"

            A->>C1: 推送进度<br/>{progress: 80%, status: "提取项目标题"}
            C1->>F: 实时推送进度
            F->>U: 更新进度条：80% "提取项目标题"

        else AI故事文本生成
            A->>C1: 推送进度<br/>{progress: 20%, status: "分析故事大纲"}
            C1->>F: 实时推送进度
            F->>U: 更新进度条：20% "分析故事大纲"

            A->>A: 调用内置AI文本处理<br/>根据大纲生成故事文本和标题
            Note over A: 项目创建阶段使用简化的AI处理<br/>复杂的AI任务（图片、视频生成）在C-1流程中进行

            A->>C1: 推送进度<br/>{progress: 50%, status: "生成完整故事文本"}
            C1->>F: 实时推送进度
            F->>U: 更新进度条：50% "生成完整故事文本"

            A->>C1: 推送进度<br/>{progress: 70%, status: "创建分镜文本描述"}
            C1->>F: 实时推送进度
            F->>U: 更新进度条：70% "创建分镜文本描述"

            A->>C1: 推送进度<br/>{progress: 85%, status: "优化分镜文本内容"}
            C1->>F: 实时推送进度
            F->>U: 更新进度条：85% "优化分镜文本内容"
        end

        %% 保存文本数据到数据库（严格遵循规范，不涉及任何文件操作）
        A->>C1: 推送进度<br/>{progress: 90%, status: "保存项目文本数据"}
        C1->>F: 实时推送进度
        F->>U: 更新进度条：90% "保存项目文本数据"

        A->>DB: UPDATE p_projects SET<br/>title=AI提取的标题, story_content=故事文本内容,<br/>storyboard=JSON分镜文本描述, <br/>project_config=JSON模块配置(story/character/image/video), status='created'
        A->>R: 缓存项目基础信息(project:{project_id})

        A->>C1: 推送进度<br/>{progress: 100%, status: "项目文本数据创建完成"}
        C1->>F: 实时推送进度
        F->>U: 更新进度条：100% "项目文本数据创建完成"

        A->>F: 返回项目创建成功<br/>{project_id, title, story_content, storyboard_text[], style_config}

        Note over A: ✅ 项目文本数据创建完成，严格遵循规范不涉及任何资源文件操作
    end

    Note over F: � 界面渲染优化

    %% 用户体验优化：处理完成后的界面更新
    F->>C1: 关闭WebSocket连接<br/>取消订阅project_progress频道
    F->>F: 隐藏处理罩层和进度条
    F->>F: 在原多行输入框位置渲染可编辑分镜文本列表<br/>每个分镜显示：场景ID、场景文字描述、预估时长、操作按钮
    F->>F: 激活"下一步：绑角色"按钮<br/>按钮变为彩色，表示满足进入下一步条件

    Note over F: ✏️ 分镜文本编辑功能（纯文本操作）

    F->>U: 显示可编辑的分镜文本列表界面<br/>支持文本操作：<br/>- 手动编辑分镜文字描述<br/>- 合并相邻分镜文本<br/>- 拖拽调整分镜顺序<br/>- 删除不需要的分镜<br/>- 添加新分镜文本

    alt 用户编辑分镜文本内容
        U->>F: 修改分镜文字描述
        F->>A: PUT /py-api/projects/{project_id}/storyboard<br/>提交修改后的分镜文本数据
        A->>DB: 更新p_projects.storyboard字段（JSON文本）
        A->>F: 返回更新成功
        F->>U: 实时更新界面显示
    end

    Note over U: ✅ 项目文本数据创建完成，资源文件生成将在C-1: AI任务调度流程中进行
```

#### Py视频创作工具业务流程C-1: AI任务调度流程（用户选择平台+环境切换版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant AiModel as AiModelController
    participant SC as AiServiceClient
    participant Mock as 虚拟AI平台
    participant DeepSeek as DeepSeek API
    participant LiblibAI as LiblibAI API
    participant KlingAI as KlingAI API
    participant MiniMax as MiniMax API
    participant Doubao as 火山引擎豆包 API
    participant DB as MySQL数据库
    participant R as Redis缓存

    Note over P: 🎬 视频创作工具启动（优化版）

    %% 一键智能推荐（合并接口125+132）
    P->>AiModel: POST /py-api/ai-models/select-platform<br/>business_type=video, auto_recommend=true
    AiModel->>SC: 调用智能平台选择服务
    SC->>DB: 查询用户历史偏好和使用记录
    SC->>SC: 分析用户偏好+平台状态+任务特性
    SC->>AiModel: 返回最佳推荐+备选方案
    AiModel->>P: 返回：推荐"KlingAI(质量最佳)"<br/>+ 备选[LiblibAI, MiniMax]

    Note over P: 🚀 用户体验优化
    alt 用户满意推荐
        P->>P: 直接点击"开始创作"<br/>使用推荐的KlingAI
    else 用户需要更多选择
        P->>P: 点击"选择其他平台"<br/>从备选方案中选择LiblibAI
    end

    Note over P: � 提交视频生成任务
    P->>A: 提交AI任务(video_generation/参数/选择的平台/Token)
    A->>A: 验证Token和任务参数
    A->>DB: 检查用户积分和权限
    A->>DB: 创建AI任务记录(含用户选择的平台+选择方式)

    A->>SC: 调用AiServiceClient(指定平台)

    Note over SC: 🚨 环境切换机制
    alt 开发环境 (AI_SERVICE_MODE=mock)
        SC->>Mock: 调用虚拟AI平台<br/>模拟用户选择的平台响应
        Mock->>SC: 返回模拟结果(快速响应)

    else 生产环境 (AI_SERVICE_MODE=real)
        alt 用户选择DeepSeek
            SC->>DeepSeek: 调用DeepSeek API
            DeepSeek->>SC: 返回生成结果
        else 用户选择LiblibAI
            SC->>LiblibAI: 调用LiblibAI API
            LiblibAI->>SC: 返回生成结果
        else 用户选择KlingAI
            SC->>KlingAI: 调用KlingAI API
            KlingAI->>SC: 返回生成结果
        else 用户选择MiniMax
            SC->>MiniMax: 调用MiniMax API
            MiniMax->>SC: 返回生成结果
        else 用户选择火山引擎豆包
            SC->>Doubao: 调用火山引擎豆包 API
            Doubao->>SC: 返回生成结果
        end
    end

    SC->>A: 返回AI生成结果(含实际使用的平台信息)
    A->>DB: 更新任务状态、结果和平台使用记录
    A->>R: 缓存任务结果
    A->>P: 返回AI生成结果

    Note over A: 📊 用户偏好学习与优化
    A->>DB: 记录用户平台选择行为<br/>(手动选择/智能推荐/选择理由)
    A->>DB: 更新用户偏好权重<br/>(质量优先/速度优先/成本优先)
    A->>R: 更新用户常用平台缓存
    A->>R: 刷新推荐算法缓存<br/>为下次推荐优化准备数据
```

### 🔄 AI核心流程

#### Py视频创作工具业务流程C-2: AI生成成功流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分(事务锁定)
    A->>DB: 扣取积分(冻结状态)
    A->>R: 同步积分状态(缓存更新)
    A->>DB: 写入业务日志(状态:冻结)
    A->>R: 缓存业务日志
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 发送AI生成请求
    AI->>SC: 返回AI生成结果
    SC->>A: 返回处理结果
    A->>DB: 确认积分扣取(解冻)
    A->>R: 更新积分缓存
    A->>DB: 更新业务日志(状态:成功)
    A->>R: 更新业务缓存
    A->>E: 发布成功事件(异步)
    A->>W: 返回成功结果
    W->>P: 推送成功通知
```

#### Py视频创作工具业务流程C-3: 积分不足业务流程（优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>A: 检查用户积分(快速验证)
    Note over A: 积分 < 所需积分
    A->>W: 返回积分不足详细信息
    W->>P: 推送积分不足消息(包含充值建议)
    Note over A: 无扣费操作，保护用户资金
```

#### Py视频创作工具业务流程C-4: 处理失败的业务流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分 → 扣取积分(冻结状态)
    A->>R: 同步积分状态 → 写入业务日志 → 缓存日志
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务(自动环境切换)
    AI->>SC: 返回失败结果
    SC->>A: 返回失败结果+环境模式信息
    A->>W: 返回失败结果
    W->>P: 推送失败结果(详细错误信息)
    W->>E: 发布任务失败事件(异步)
    E->>A: 处理任务失败事件
    A->>DB: 更新日志状态(失败) + 返还等额积分(事务保证)
    A->>R: 更新缓存状态
    Note over SC: 环境切换完成，积分安全返还
```

#### Py视频创作工具业务流程C-5: 超时/中断处理业务流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant T as 超时监控
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分 → 扣取积分(冻结状态)
    A->>R: 同步积分状态 → 写入业务日志 → 缓存日志
    A->>SC: 调用AiServiceClient
    W->>T: 启动超时监控(业务类型自适应)
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务(自动环境切换)

    alt 超时或连接中断
        T->>E: 检测到超时/中断(发布事件)
        E->>A: 处理中断事件
        A->>DB: 更新日志状态(失败) + 返还等额积分
        A->>R: 更新缓存状态
        E->>W: 通知WebSocket服务
        W->>P: 推送中断消息(包含积分返还确认)
        Note over SC: 环境切换机制保证服务稳定性
    end
```



#### Py视频创作工具业务流程C-6: AI资源生成与版本管理流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant RM as AI资源管理服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 发起AI资源生成请求(含module_id)
    A->>RM: 创建资源记录
    RM->>DB: 创建p_resources记录
    RM->>DB: 自动创建v1.0版本记录
    RM->>A: 返回资源UUID
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务生成资源
    AI->>SC: 返回资源URL和元数据
    SC->>A: 返回结果+环境模式信息
    A->>RM: 更新版本信息
    RM->>DB: 更新resource_url、file_size等
    RM->>RM: 执行自动内容审核
    RM->>DB: 更新review_status
    RM->>R: 缓存资源状态
    A->>P: 返回资源信息
    P->>AI: 直接下载资源到本地
    P->>A: 确认下载完成
    A->>RM: 更新下载状态
    RM->>DB: 更新downloaded_by_python=true
```

#### Py视频创作工具业务流程C-7: 资源下载完成流程（核心流程）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库

    P->>A: 请求资源下载信息(resource_id)
    A->>DB: 查询资源信息和AI平台URL
    DB->>A: 返回resource_url和元数据
    A->>P: 返回AI平台URL和文件信息
    Note over SC: 🚨环境切换：资源URL根据环境<br/>指向模拟服务或真实AI平台
    P->>AI: 直接从AI平台下载资源
    AI->>P: 下载完成
    P->>A: 确认下载完成(local_path)
    A->>DB: 更新下载状态和本地路径
    Note over P: 创作完成，资源已保存到本地
```

#### Py视频创作工具业务流程C-8: 可选作品发布流程（增值服务）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant WPS as 作品发布权限服务
    participant WP as 作品广场
    participant DB as MySQL数据库

    Note over P: 用户已完成本地导出
    P->>A: [可选] 请求发布作品(module_type, module_id)
    A->>WPS: 检查发布权限
    WPS->>DB: 查询模块相关资源的review_status

    alt 用户选择发布
        DB->>WPS: review_status = 'approved'/'auto_approved'
        WPS->>A: 返回允许发布
        A->>WP: 创建作品广场记录
        WP->>DB: 保存到p_work_plaza表
        WP->>A: 返回发布成功
        A->>P: 通知发布成功
    else 用户选择不发布
        P->>A: 跳过发布，仅本地保存
        A->>P: 确认完成，无需发布
    end
```

#### Py视频创作工具业务流程C-9: 环境切换机制流程（核心机制）
```mermaid
sequenceDiagram
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant Config as 配置系统
    participant Mock as 模拟服务
    participant Real as 真实AI服务

    A->>SC: 请求AI服务调用
    SC->>Config: 读取AI_SERVICE_MODE配置

    alt 开发环境 (mock模式)
        Config->>SC: 返回mode=mock
        SC->>Mock: 调用模拟服务
        Mock->>SC: 返回模拟结果+mode=mock
        SC->>A: 返回结果{success:true, mode:'mock', data:...}
        Note over SC: 开发环境：无真实费用，快速响应
    else 生产环境 (real模式)
        Config->>SC: 返回mode=real
        SC->>Real: 调用真实AI服务
        Real->>SC: 返回真实结果
        SC->>A: 返回结果{success:true, mode:'real', data:...}
        Note over SC: 生产环境：真实调用，产生费用
    end

    Note over SC: 环境切换对业务层透明<br/>统一的调用接口和响应格式
```



## 🌐 WEB网页工具业务流程图

### 🌐 WEB网页工具核心业务流程

#### WEB网页工具1: 用户注册登录流程
```mermaid
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方服务

    W->>A: 用户注册请求(用户名/邮箱/密码)
    A->>DB: 检查用户名/邮箱是否存在
    alt 用户已存在
        DB->>A: 返回用户已存在
        A->>W: 返回注册失败(用户已存在)
    else 用户不存在
        A->>DB: 创建新用户记录
        A->>TP: 发送验证邮件/短信
        TP->>A: 返回发送结果
        A->>R: 缓存验证码
        A->>W: 返回注册成功(待验证)

        Note over W: 用户点击验证链接
        W->>A: 验证请求(验证码)
        A->>R: 验证验证码
        A->>DB: 激活用户账户
        A->>W: 返回验证成功

        W->>A: 登录请求(用户名/密码)
        A->>DB: 验证用户凭据
        A->>R: 生成并存储Token
        A->>W: 返回登录成功(Token)
    end
```

#### WEB网页工具2: 作品广场浏览流程
```mermaid
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    W->>A: 请求作品列表(分类/搜索条件)
    A->>R: 检查缓存中的作品列表
    alt 缓存命中
        R->>A: 返回缓存的作品列表
    else 缓存未命中
        A->>DB: 查询作品数据(p_work_plaza)
        DB->>A: 返回作品列表
        A->>R: 更新缓存
    end
    A->>W: 返回作品列表(含缩略图)

    Note over W: 用户点击查看作品详情
    W->>A: 请求作品详情(work_id)
    A->>DB: 查询作品详细信息
    A->>DB: 更新作品浏览次数
    A->>W: 返回作品详情(含高清图/视频)
```

#### WEB网页工具3: 用户中心管理流程
```mermaid
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方支付

    W->>A: 请求用户信息(Token)
    A->>R: 验证Token有效性
    A->>DB: 查询用户详细信息
    A->>W: 返回用户信息(积分/等级/统计)

    Note over W: 用户充值积分
    W->>A: 发起充值请求(金额)
    A->>TP: 调用支付接口
    TP->>A: 返回支付链接
    A->>W: 返回支付链接

    Note over W: 用户完成支付
    TP->>A: 支付回调通知
    A->>DB: 更新用户积分
    A->>R: 更新积分缓存
    A->>W: 推送充值成功通知
```

## 🏢 管理后台业务流程图

### 🏢 管理后台核心业务流程

#### 管理后台业务流程1: 系统配置管理流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant AI as AI平台配置

    M->>A: 管理员登录(用户名/密码)
    A->>DB: 验证管理员权限
    A->>R: 生成管理员Token
    A->>M: 返回登录成功(AdminToken)

    M->>A: 请求AI平台配置列表
    A->>DB: 查询AI平台配置
    A->>M: 返回配置列表(API密钥/地址)

    Note over M: 管理员修改AI平台配置
    M->>A: 更新AI平台配置(平台/密钥/地址)
    A->>AI: 测试新配置连通性
    alt 配置测试成功
        A->>DB: 保存新配置
        A->>R: 更新配置缓存
        A->>M: 返回配置更新成功
    else 配置测试失败
        A->>M: 返回配置测试失败(错误详情)
    end
```

#### 管理后台业务流程2: 用户管理流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    M->>A: 请求用户列表(分页/筛选条件)
    A->>DB: 查询用户数据(p_users)
    A->>M: 返回用户列表(基本信息/状态)

    Note over M: 管理员查看用户详情
    M->>A: 请求用户详情(user_id)
    A->>DB: 查询用户详细信息
    A->>DB: 查询用户积分记录
    A->>DB: 查询用户作品统计
    A->>M: 返回用户完整信息

    Note over M: 管理员操作用户账户
    M->>A: 用户账户操作(禁用/启用/积分调整)
    A->>DB: 更新用户状态/积分
    A->>R: 更新用户缓存
    alt 账户被禁用
        A->>R: 清除用户Token
    end
    A->>M: 返回操作成功
```

#### 管理后台业务流程3: 内容审核管理流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant U as 用户通知

    M->>A: 请求待审核内容列表
    A->>DB: 查询待审核作品(p_work_plaza)
    A->>M: 返回待审核列表(作品信息/提交时间)

    Note over M: 管理员审核作品
    M->>A: 请求作品详情(work_id)
    A->>DB: 查询作品完整信息
    A->>M: 返回作品详情(内容/资源/用户信息)

    M->>A: 提交审核结果(通过/拒绝/原因)
    A->>DB: 更新作品审核状态
    alt 审核通过
        A->>DB: 发布作品到广场
        A->>R: 更新作品缓存
        A->>U: 发送审核通过通知
    else 审核拒绝
        A->>U: 发送审核拒绝通知(含原因)
    end
    A->>M: 返回审核操作成功
```

#### 管理后台业务流程4: 数据统计分析流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    M->>A: 请求系统统计数据(时间范围)
    A->>R: 检查统计数据缓存
    alt 缓存命中且未过期
        R->>A: 返回缓存的统计数据
    else 缓存未命中或已过期
        A->>DB: 查询用户统计数据
        A->>DB: 查询收入统计数据
        A->>DB: 查询AI使用统计
        A->>DB: 查询作品发布统计
        A->>A: 汇总分析统计数据
        A->>R: 缓存统计结果(30分钟)
    end
    A->>M: 返回统计报表(图表数据)

    Note over M: 管理员导出报表
    M->>A: 请求导出报表(格式/范围)
    A->>A: 生成报表文件(Excel/PDF)
    A->>M: 返回报表下载链接
```

## 📊 图表使用说明

### 📋 图表分类说明

本文档包含以下类型的图表：

1. **系统架构图表**
   - 完整系统架构图（环境切换优化版）
   - AI服务集成模拟机制架构图
   - AI服务调用流程对比图
   - 项目依赖关系图

2. **Py视频创作工具业务流程图**
   - 用户管理业务流程（A-1到A-5：注册、登录、Token验证、密码管理）
   - 业务功能流程（B-1到B-5：充值积分、积分管理、代理推广、代理结算、数据处理）
   - 项目管理流程（C-0：视频创作项目创建流程）
   - AI核心流程（C-1到C-8：AI任务调度、AI生成成功、积分不足、处理失败、超时中断、资源管理、资源下载、作品发布、环境切换）
   - 核心业务流程（1到4：处理成功、积分不足优化版、处理失败、超时中断）

3. **WEB网页工具业务流程图**
   - WEB网页工具1：用户注册登录流程
   - WEB网页工具2：作品广场浏览流程
   - WEB网页工具3：用户中心管理流程

4. **管理后台业务流程图**
   - 系统配置管理流程
   - 用户管理流程
   - 内容审核管理流程
   - 数据统计分析流程

### 🎯 图表使用指南

- 所有图表均使用 Mermaid 语法编写
- 图表支持在支持 Mermaid 的环境中直接渲染
- 每个图表都包含详细的流程说明和关键节点标注
- 图表按照业务逻辑分组，便于查找和使用
- 环境切换相关的图表特别标注了 mock/real 模式的区别

### 🔄 图表更新说明

本图表集合从 index-new.mdc 迁移而来，包含了完整的系统架构和业务流程设计。如需更新图表，请同时更新此文档以保持一致性。

## 📋 核心组件功能职责规范索引

### 🎯 AI程序员行为规范化指南

为增强AI程序员对系统架构的理解和行为规范化，以下是各核心组件的功能职责规范索引，详细内容请参考 **[index-new.mdc](.cursor/rules/index-new.mdc)**：

#### 🔧 工具API接口服务功能职责矩阵

**1. Py视频创作工具API接口职责** (`@php/api/app/Http/Controllers/PyApi`)
- ✅ **核心AI功能**: 创建AI创作视频任务、AI任务调度（文生文、图生图、图生视频、语音、音效、音乐）
- ✅ **用户管理**: 用户注册登录、资料修改、密码找回、用户认证
- ✅ **积分系统**: 充值积分、积分管理、积分明细
- ✅ **代理系统**: 代理推广、代理结算
- ✅ **数据处理**: 数据处理、作品发布
- ✅ **实时通信**: WebSocket服务（仅限Py视频创作工具）
- ❌ **禁止职责**: 不储存且不中转用户创作过程中AI生成的资源、视频编辑处理、客户端UI逻辑、本地文件操作

**2. WEB网页工具API接口职责** (`@php/api/app/Http/Controllers/WebApi`)
- ✅ **展示功能**: 功能介绍查询、价格方案查询、作品数据查询（分类筛选、搜索查看、作品详情展示）
- ✅ **用户中心**: 用户注册登录、资料修改、密码找回、用户认证、充值积分、积分管理、积分明细
- ✅ **代理功能**: 代理推广、代理结算
- ✅ **响应式设计**: 支持PC端、移动端、Py视频创作工具嵌入（1200px/800px窗口）
- ❌ **严格禁止**: 视频创作功能、AI生成功能、WebSocket实时通信、作品发布创建

**3. 管理后台API接口职责** (`@php/api/app/Http/Controllers/AdminApi`)
- ✅ **系统配置**: AI平台配置、系统参数设置
- ✅ **用户管理**: 用户信息、权限管理、账户状态
- ✅ **内容管理**: 作品审核、内容监控、违规处理
- ✅ **数据统计**: 用户统计、收入统计、使用情况分析
- ✅ **积分系统**: 积分规则、充值记录、消费明细
- ✅ **代理系统**: 代理审核、佣金结算、推广数据
- ✅ **素材库管理**: 音色库、音效库、音乐库、风格库、角色库
- ✅ **系统监控**: 性能监控、错误日志、API调用统计
- ✅ **财务管理**: 收入报表、退款处理、财务对账

#### 🚨 关键架构边界规范

**资源下载架构铁律**:
- ✅ **Py视频创作工具**: 必须直接从AI平台下载资源到本地
- ❌ **API服务器**: 严禁进行资源文件的中转下载、生成、处理、存储
- ✅ **服务器职责**: 仅负责管理资源的URL、状态、元数据等附件信息

**WebSocket使用边界**:
- ✅ **仅Py视频创作工具使用**: AI生成进度推送、任务状态通知
- ❌ **WEB工具禁用**: 避免不必要的连接和资源消耗
- 🔒 **安全传输**: 密钥加密传输，不持久化存储

#### 📊 WEB网页工具功能边界详细规范

**✅ 允许的功能职责**:
1. **展示职责**: 首页工具展示、功能介绍、价格方案展示、平台统计数据展示、公告和帮助信息
2. **用户中心**: 用户注册登录认证、资料管理修改、密码找回安全设置、积分查询充值明细
3. **作品广场**: 作品展示浏览、分类筛选搜索、作品详情查看、作品互动（点赞、分享）
4. **代理推广**: 代理申请管理、推广统计数据、佣金查询结算、推广链接生成
5. **响应式设计**: PC端完整功能体验、移动端优化适配、Py视频创作工具嵌入适配

**❌ 禁止的功能职责**:
1. **创作功能禁止**: 视频创作编辑、AI内容生成、素材处理合成、本地文件操作
2. **实时通信禁止**: WebSocket连接、实时进度推送、即时消息通信、长连接维护
3. **作品发布禁止**: 作品创建上传、作品发布到广场、作品审核管理、作品版本控制
4. **高级管理禁止**: 系统配置管理、用户权限管理、数据统计分析、内容审核操作

### 🎯 AI程序员开发指导原则

1. **严格遵循职责边界**: 每个组件只能执行其明确定义的职责范围内的功能
2. **禁止跨界操作**: 不得在组件中实现其禁止职责列表中的任何功能
3. **架构铁律优先**: 资源下载铁律和WebSocket使用边界不可违反
4. **环境切换机制**: 所有AI和第三方服务调用必须支持mock/real模式切换
5. **性能指标遵循**: 并发支持1000用户，响应时间符合规定标准

### 📚 详细规范文档参考

- **完整功能职责定义**: [index-new.mdc - 🎯 核心组件职责定义](.cursor/rules/index-new.mdc#核心组件职责定义)
- **WEB工具API接口列表**: [index-new.mdc - 📋 WEB网页工具API接口列表](.cursor/rules/index-new.mdc#web网页工具api接口列表)
- **架构边界规范**: [index-new.mdc - 🚨 关键架构原则](.cursor/rules/index-new.mdc#关键架构原则)
- **开发文档使用指南**: [index-new.mdc - 📚 开发文档使用指南](.cursor/rules/index-new.mdc#开发文档使用指南)