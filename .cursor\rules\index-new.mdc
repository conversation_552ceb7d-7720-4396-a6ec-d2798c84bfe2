---
description: AI视频创作工具系统架构规范
globs:
alwaysApply: true
---

# AI视频创作工具系统架构规范

## 📋 项目概述

### 🎯 项目定位
本项目是一个完整的AI视频创作工具生态系统，包含五大核心组件：
- **Py视频创作工具**：客户端视频创作应用
- **WEB网页工具**：展示和用户中心功能
- **管理后台**：系统管理和内容审核
- **工具API接口服务**：统一API接口服务
- **AI服务集成模拟**：开发环境AI服务模拟

**📁 文档路径说明**: 本文档中提到的所有开发规范文档都位于 `@.cursor/rules/` 目录下。

### 🏗️ 系统架构设计原则
- **职责边界清晰**：每个组件职责明确，避免功能重叠
- **服务解耦**：组件间通过标准API接口通信，降低耦合度
- **资源本地化**：用户创作资源由Py视频创作工具直接从AI平台下载到本地
- **可选发布机制**：作品发布为增值服务，用户可选择是否发布到广场
- **环境切换机制**：支持开发环境（mock）和生产环境（real）无缝切换


## 📁 项目目录结构

```
项目根目录/
├── php/                  # PHP 后端服务
│   ├── backend/          # 管理后台（Vue 3 + Element Plus 前端）
│   ├── api/              # 工具API接口服务（Lumen 10）
│   ├── web/              # WEB网页工具（Vue 3 + Element Plus 前端）
│   ├── aiapi/            # AI服务集成模拟（开发环境）
│   └── thirdapi/         # 第三方服务集成模拟（开发环境）
├── python/               # Py视频创作工具（Python 3.12）
└── .cursor/rules/        # 开发规范文档（19个）
    ├── index-new.mdc                         # 📋 主规范文档
    ├── dev-chart-guidelines.mdc              # 📊 架构图表和业务流程规范
    ├── dev-api-guidelines-database.mdc       # 🗄️ 数据库设计规范
    ├── dev-api-guidelines-add.mdc            # ➕ 新功能开发规范
    ├── dev-api-guidelines-edit.mdc           # 🔧 问题修复和优化规范
    ├── dev-api-guidelines-pyapi.mdc          # 🐍 Py视频创作工具API文档规范
    ├── dev-api-guidelines-webapi.mdc         # 🌐 WEB网页工具API文档规范
    ├── dev-api-guidelines-adminapi.mdc       # 🏢 管理后台API文档规范
    ├── dev-aiapi-guidelines.mdc              # 🤖 AI服务集成开发规范（包含5个AI平台API文档）
    ├── dev-thirdapi-guidelines.mdc           # 🔗 第三方服务集成规范（包含第三方服务API文档）
    ├── dev-vue3-guidelines.mdc               # 🟢 Vue 3 + Element Plus 开发规范
    ├── dev-lumen10-guidelines.mdc            # ⚡ Lumen 10 开发规范
    ├── dev-php8.1.29-guidelines.mdc          # 🐘 PHP 8.1.29 开发规范
    ├── dev-python3.12-guidelines.mdc         # 🐍 Python 3.12 开发规范
    ├── dev-mysql8.0-guidelines.mdc           # 🗄️ MySQL 8.0 开发规范
    ├── ai-api-deepseek.com-guidelines.mdc    # 🧠 DeepSeek AI API 规范
    ├── ai-api-liblib.art-guidelines.mdc      # 🎨 LiblibAI API 规范
    ├── ai-api-klingai.com-guidelines.mdc     # 🎬 KlingAI API 规范
    ├── ai-api-minimaxi.com-guidelines.mdc    # 🔮 MiniMax AI API 规范
    └── ai-api-volcengine.com-guidelines.mdc  # 🌋 火山引擎豆包 API 规范
```

## 🎯 核心组件职责定义

### 1. ⚡ 工具API接口服务 (@php/api/)
**核心职责**：为 `Py视频创作工具` 和 `WEB网页工具` 和 `管理后台` 提供后端API接口服务
- **后端技术栈**：Lumen 10 + PHP 8.1.29 + MySQL 8.0 + Redis 7.4.2
- **业务逻辑**：处理所有业务逻辑和数据管理
- **依赖服务**：调用 `AI服务集成模拟` 和 `第三方服务集成模拟` 这两个组件的API接口
- **用户认证**：Token认证和权限管理
- **数据存储**：MySQL数据存储和Redis缓存
- **Web服务器**：Nginx 1.26.2（生产环境）
- **接口分类**：
  - **PyApi** (@php/api/app/Http/Controllers/PyApi): Py视频创作工具专用API接口
  - **WebApi** (@php/api/app/Http/Controllers/WebApi): WEB网页工具专用API接口
  - **AdminApi** (@php/api/app/Http/Controllers/AdminApi): 管理后台专用API接口
- **开发规范**：`dev-api-guidelines-add.mdc` + `dev-lumen10-guidelines.mdc`
- **API文档规范**：
  - **PyApi文档**：`dev-api-guidelines-pyapi.mdc`
  - **WebApi文档**：`dev-api-guidelines-webapi.mdc`
  - **AdminApi文档**：`dev-api-guidelines-adminapi.mdc`

### 2. 🐍 Py视频创作工具 (@python/)
**核心职责**：AI视频创作的主要客户端应用
- **API接口支持**：统一调用 `工具API接口服务` 这个组件的PyApi接口
- **客户端技术栈**：Python 3.12 + PySide6 + PyInstaller
- **用户界面**：提供完整的视频创作操作界面（PySide6 GUI）
- **AI功能集成**：调用工具API接口服务进行AI内容生成
- **实时通信**：WebSocket客户端，接收AI生成进度推送
- **资源管理**：直接从AI平台下载资源到本地
- **视频编辑**：本地视频编辑和导出功能
- **打包部署**：PyInstaller打包为Windows/Mac可执行文件
- **支持平台**：Windows 10/11、macOS（客户端应用）
- **开发规范**：`dev-python3.12-guidelines.mdc` + `index-new.mdc`
- **对接API文档规范**：`dev-api-guidelines-pyapi.mdc`

### 3. 🏢 管理后台 (@php/backend/)
**核心职责**：系统管理和内容审核（前后端分离架构）
- **API接口支持**：统一调用 `工具API接口服务` 这个组件的AdminApi接口
- **前端技术栈**：Vue 3 + Element Plus + TypeScript + Vite
- **用户管理**：用户账户和权限管理
- **内容审核**：作品发布审核和管理
- **系统监控**：系统状态和性能监控
- **数据统计**：业务数据分析和报表
- **部署方式**：静态前端部署，通过AdminApi接口与后端通信
- **Web服务器**：Nginx 1.26.2（静态文件服务）
- **构建工具**：Vite（开发和生产构建）
- **开发规范**：`dev-vue3-guidelines.mdc` + `index-new.mdc`
- **对接API文档规范**：`dev-api-guidelines-adminapi.mdc`

### 4. 🌐 WEB网页工具 (@php/web/)
**核心职责**：用户展示和社区功能（前后端分离架构）
- **API接口支持**：统一调用 `工具API接口服务` 这个组件的WebApi接口
- **前端技术栈**：Vue 3 + Element Plus + TypeScript + Vite
- **作品展示**：作品广场和用户作品展示
- **用户中心**：用户信息管理和设置
- **社交功能**：点赞、评论、分享等互动
- **响应式设计**：支持PC端、移动端自适应
- **部署方式**：静态前端部署，通过WebApi接口与后端通信
- **Web服务器**：Nginx 1.26.2（静态文件服务）
- **构建工具**：Vite（开发和生产构建）
- **开发规范**：`dev-vue3-guidelines.mdc` + `index-new.mdc`
- **API文档规范**：`dev-api-guidelines-webapi.mdc`

### 5. 🤖 AI服务集成模拟 (@php/aiapi/)
**核心职责**：集成第三方AI平台的虚拟服务（开发环境专用临时组件）
- **后端技术栈**：Lumen 10 + PHP 8.1.29（轻量级API服务）
- **AI平台集成**：集成DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包等第三方AI平台
- **虚拟服务模拟**：在保持不同AI平台API接口特性前提下提供统一的模拟响应
- **开发支持**：支持工具API接口服务的本地开发，避免真实AI调用费用
- **环境切换**：通过 `AI_SERVICE_MODE` 环境变量支持mock/real模式切换
- **生产环境**：上线后通过配置修改直连真实AI平台，无需修改代码
- **服务地址**：`https://aiapi.tiptop.cn/`（开发环境专用）
- **Web服务器**：Nginx 1.26.2（开发环境）
- **开发规范**：`dev-aiapi-guidelines.mdc` + `index-new.mdc`
- **API文档规范**：`dev-aiapi-guidelines.mdc`（包含5个AI平台的完整API接口规范）

**🚨 重要特性**：
- **第三方AI平台集成**：根据第三方AI平台的API接口文档接收和模拟返回数据
- **无缝切换**：开发环境使用虚拟服务，生产环境自动切换到真实AI平台
- **费用控制**：开发阶段完全无AI调用费用产生，避免误操作成本
- **完全兼容**：虚拟服务API与真实AI平台保持100%一致
- **安全隔离**：开发数据与生产数据完全隔离

### 6. 🔗 第三方服务集成模拟 (@php/thirdapi/)
**核心职责**：开发环境第三方服务模拟（临时组件）
- **后端技术栈**：Lumen 10 + PHP 8.1.29（轻量级API服务）
- **模拟响应**：模拟第三方服务API响应，保持100%兼容性
- **开发支持**：支持本地开发和测试，避免真实费用产生
- **支持服务**：微信OAuth/支付、支付宝支付、短信服务、邮件服务
- **环境切换**：通过 `THIRD_PARTY_MODE` 环境变量控制切换
- **生产环境**：上线后通过配置直连真实第三方服务，无需修改代码
- **服务地址**：`https://thirdapi.tiptop.cn/`（开发环境专用）
- **Web服务器**：Nginx 1.26.2（开发环境）
- **开发规范**：`dev-thirdapi-guidelines.mdc`
- **API文档规范**：`dev-thirdapi-guidelines.mdc`（包含微信、支付宝、短信、邮件服务的完整API接口规范）

**🚨 重要特性**：
- **无缝切换**：开发环境使用模拟服务，生产环境自动切换到真实服务
- **费用控制**：开发阶段完全无费用产生，避免误操作成本
- **完全兼容**：模拟服务API与真实服务保持100%一致
- **安全隔离**：开发数据与生产数据完全隔离

## 📊 系统架构与业务流程

### 🏗️ 完整系统架构
**详细架构图**: 参见 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)**

**架构层次结构**：
- **用户层**: Py视频创作工具、WEB网页工具、管理后台
- **业务服务层**: 工具API接口服务、WebSocket服务、AI资源管理服务
- **环境切换层**: AiServiceClient、ThirdPartyServiceClient
- **开发支持层**: AI服务模拟、第三方服务模拟（开发环境专用）
- **数据存储层**: MySQL数据库、Redis缓存
- **外部服务层**: 5个AI平台、第三方服务

### 🔄 业务流程图集合
**详细流程图**: 参见 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)**

**包含流程**：
- **Py视频创作工具**: 用户管理、业务功能、项目管理、AI核心流程
- **WEB网页工具**: 用户中心、作品展示、社交互动
- **管理后台**: 用户管理、内容审核、系统监控
- **环境切换机制**: mock/real模式切换流程

### 🎯 关键架构特性
1. **职责边界清晰**: 每个组件职责明确，避免功能重叠
2. **环境切换机制**: 开发/生产环境无缝切换
3. **避免循环依赖**: 异步事件驱动架构
4. **WebSocket边界**: 仅Py视频创作工具使用实时通信
5. **性能优化**: 支持1000并发用户，MySQL+Redis双重保障
6. **安全架构**: 密钥安全传输，权限二次验证
7. **资源本地化**: 用户资源直接从AI平台下载到本地

## 🔄 环境切换机制

### 🎯 环境切换核心价值
**详细机制图**: 参见 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)**

**关键优势**：
- ✅ **开发效率**: 本地开发无需依赖真实AI平台和第三方服务
- ✅ **成本控制**: 避免开发阶段产生真实费用
- ✅ **测试完整性**: 模拟各种边界情况和异常状态
- ✅ **完全兼容**: 确保与真实平台API的100%兼容性
- ✅ **架构纯净**: 工具API接口服务保持业务逻辑纯净
- ✅ **安全隔离**: 模拟环境与真实环境完全隔离

### 🔀 AI服务切换机制(本地开发和测试必须使用`mock`)
- **开发环境**: 工具API → `@php/aiapi/` AI服务模拟 → 模拟数据返回
- **生产环境**: 工具API → 真实AI平台（DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包） → 真实数据返回
- **切换方式**: 通过 `AI_SERVICE_MODE` 环境变量控制（mock/real）
- **调用方法**: 使用 `AiServiceClient::callWithUserChoice()` 统一调用
- **模拟组件**: `@php/aiapi/` 集成第三方AI平台的虚拟服务

### 🔗 第三方服务切换机制(本地开发和测试必须使用`mock`)
- **开发环境**: 工具API → `@php/thirdapi/` 第三方服务模拟 → 模拟数据返回
- **生产环境**: 工具API → 真实第三方平台（微信/支付宝/短信/邮件服务） → 真实数据返回
- **切换方式**: 通过 `THIRD_PARTY_MODE` 环境变量控制（mock/real）
- **调用方法**: 使用 `ThirdPartyServiceClient::call()` 统一调用
- **模拟组件**: `@php/thirdapi/` 集成第三方服务的虚拟服务

### 🚨 环境切换铁律
1. **代码无修改切换**: 上线时仅需修改环境变量配置，无需修改任何代码
2. **统一调用接口**: 所有AI服务和第三方服务都通过统一的客户端调用
3. **配置驱动**: 环境切换完全通过配置文件驱动，不得硬编码
4. **100%兼容**: 模拟服务必须与真实服务API保持100%兼容

## 🎯 API接口服务详细职责

### 📱 PyApi - Py视频创作工具专用API
**控制器目录**: `@php/api/app/Http/Controllers/PyApi`
**业务层目录**: `@php/api/app/Services/PyApi`
**开发规范**: `dev-api-guidelines-pyapi.mdc`

**核心功能**：
- **AI任务调度**: 文生文、图生图、图生视频、语音生成、音效音乐生成
- **项目管理**: 视频创作项目创建、编辑、管理
- **用户管理**: 注册登录、资料修改、密码找回、用户认证
- **积分系统**: 充值积分、积分管理、积分明细
- **代理系统**: 代理推广、代理结算
- **作品管理**: 作品发布、数据处理
- **实时通信**: WebSocket AI生成进度推送（仅Py工具使用）

**架构边界**：
- ✅ 负责AI任务调度和进度管理
- ✅ 支持WebSocket实时通信
- ❌ 不储存且不中转用户创作过程中AI生成的资源
- ❌ 不处理视频编辑、客户端UI逻辑、本地文件操作

### 🌐 WebApi - WEB网页工具专用API
**控制器目录**: `@php/api/app/Http/Controllers/WebApi`
**业务层目录**: `@php/api/app/Services/WebApi`
**开发规范**: `dev-api-guidelines-webapi.mdc` + `dev-lumen10-guidelines.mdc`

**核心功能**：
- **展示服务**: 功能介绍、价格方案查询、平台统计数据
- **作品广场**: 作品数据查询、分类筛选、搜索查看、作品详情展示
- **用户中心**: 注册登录、资料修改、密码找回、用户认证
- **积分系统**: 充值积分、积分管理、积分明细
- **代理系统**: 代理推广、代理结算
- **社交功能**: 作品点赞、评论、分享等互动
- **响应式支持**: 为PC端、移动端、Py工具嵌入提供API支持

**架构边界**：
- ✅ 负责展示和社交功能API
- ❌ 不包含视频创作功能、AI生成功能
- ❌ 不支持WebSocket实时通信
### 🏢 AdminApi - 管理后台专用API
**控制器目录**: `@php/api/app/Http/Controllers/AdminApi`
**业务层目录**: `@php/api/app/Services/AdminApi`
**开发规范**: `dev-api-guidelines-adminapi.mdc` + `dev-lumen10-guidelines.mdc`

**核心功能**：
- **用户管理**: 用户账户管理、权限控制、用户统计
- **内容审核**: 作品发布审核、内容管理、违规处理
- **系统监控**: 系统状态监控、性能分析、错误日志
- **数据统计**: 业务数据分析、报表生成、收入统计
- **系统配置**: AI平台配置、系统参数设置、功能开关
- **素材管理**: 音色库、音效库、音乐库、风格库、角色库管理
- **财务管理**: 收入报表、退款处理、财务对账

**架构边界**：
- ✅ 负责系统管理和数据分析
- ❌ 不包含视频创作功能、AI生成功能
- ❌ 不支持WebSocket实时通信

## 🗄️ 三大API分类共用数据表规范

### 📋 数据表共享原则
**数据库设计规范**: 详见 `dev-api-guidelines-database.mdc`

**核心原则**：
- ✅ **统一数据源**: 三大API分类共享同一套MySQL数据库和Redis缓存
- ✅ **数据一致性**: 确保跨API分类的数据操作保持一致性
- ✅ **权限隔离**: 通过业务逻辑层控制不同API分类的数据访问权限
- ✅ **事务管理**: 跨API分类的数据操作需要统一的事务管理

### 🔗 共用数据表分类

#### **基础业务表** (PyApi + WebApi + AdminApi 共用)
- **p_users**: 用户表 - 用户基础信息、认证信息
- **p_points_transactions**: 积分交易表 - 积分流水记录
- **p_points_freeze**: 积分冻结表 - 积分冻结机制

#### **AI生成相关表** (PyApi 主用 + AdminApi 管理)
- **p_ai_model_configs**: AI模型配置表 - AI平台配置管理
- **p_style_library**: 风格库表 - 剧情风格管理
- **p_character_library**: 角色库表 - AI生成角色信息
- **p_character_categories**: 角色分类表 - 角色分类管理
- **p_music_library**: 音乐库表 - AI生成音乐存储
- **p_sound_library**: 音效库表 - AI生成音效存储
- **p_timbre_library**: 音色库表 - AI生成音色存储
- **p_story_library**: 故事库表 - AI生成故事内容

#### **核心资源管理表** (PyApi 主用 + AdminApi 管理)
- **p_resources**: AI生成资源表 - 资源管理、状态跟踪
- **p_resource_versions**: 资源版本表 - 版本控制、提示词管理

#### **任务管理表** (PyApi 主用 + AdminApi 监控)
- **p_ai_generation_tasks**: AI生成任务表 - 任务状态、进度、结果
- **p_websocket_sessions**: WebSocket会话表 - 连接管理、状态同步

#### **项目管理表** (PyApi 主用 + AdminApi 管理)
- **p_projects**: 项目表 - 视频创作项目管理
- **p_project_collaborators**: 项目协作者表 - 项目协作管理

#### **用户成长系统表** (PyApi + WebApi 共用 + AdminApi 管理)
- **p_user_levels**: 用户等级表 - 等级系统配置
- **p_achievements**: 成就表 - 成就系统
- **p_user_achievements**: 用户成就表 - 用户成就记录
- **p_daily_tasks**: 每日任务表 - 每日任务系统
- **p_user_daily_tasks**: 用户每日任务表 - 用户每日任务记录
- **p_growth_histories**: 用户成长历史表 - 成长轨迹记录

#### **社交功能表** (WebApi 主用 + PyApi 部分使用 + AdminApi 管理)
- **p_user_works**: 用户作品表 - 用户创作的作品管理
- **p_work_plaza**: 作品广场表 - 公开作品展示平台
- **p_work_shares**: 作品分享表 - 分享链接、权限控制
- **p_follows**: 关注关系表 - 用户关注系统
- **p_likes**: 点赞表 - 通用点赞系统
- **p_comments**: 评论表 - 通用评论系统
- **p_work_interactions**: 作品互动表 - 互动统计汇总

### 🚨 数据访问权限规范

#### **PyApi 数据访问权限**
- ✅ **完全访问**: 基础业务表、AI生成相关表、资源管理表、任务管理表、项目管理表
- ✅ **读写访问**: 用户成长系统表、部分社交功能表（用户作品、作品发布）
- ❌ **禁止访问**: 系统管理表、财务管理表

#### **WebApi 数据访问权限**
- ✅ **完全访问**: 基础业务表、社交功能表、用户成长系统表
- ✅ **只读访问**: AI生成相关表（风格库、角色库等展示数据）
- ❌ **禁止访问**: 任务管理表、项目管理表、系统管理表

#### **AdminApi 数据访问权限**
- ✅ **完全访问**: 所有数据表（管理员权限）
- ✅ **监控访问**: 任务管理表、WebSocket会话表
- ✅ **审核管理**: 社交功能表、用户作品表

### 🔄 数据一致性保障

#### **跨API分类事务管理**
- **用户注册**: PyApi/WebApi 创建用户 → AdminApi 可管理
- **积分操作**: PyApi 消费积分 → WebApi 查询积分 → AdminApi 管理积分
- **作品发布**: PyApi 创建作品 → WebApi 展示作品 → AdminApi 审核作品
- **用户成长**: PyApi 触发成长 → WebApi 展示成长 → AdminApi 管理成长

#### **数据同步机制**
- **Redis缓存同步**: 确保三大API分类的缓存数据一致性
- **数据库事务**: 跨API分类的数据操作使用统一事务管理
- **状态同步**: 关键业务状态变更需要同步到相关API分类

### 📊 数据表使用矩阵

| 数据表分类 | PyApi | WebApi | AdminApi | 主要用途 |
|-----------|-------|--------|----------|----------|
| **基础业务表** | 读写 | 读写 | 管理 | 用户认证、积分管理 |
| **AI生成相关表** | 读写 | 只读 | 管理 | AI内容生成、素材管理 |
| **资源管理表** | 读写 | 禁止 | 管理 | AI生成资源管理 |
| **任务管理表** | 读写 | 禁止 | 监控 | AI任务调度、进度管理 |
| **项目管理表** | 读写 | 禁止 | 管理 | 视频创作项目管理 |
| **用户成长表** | 读写 | 读写 | 管理 | 用户等级、成就系统 |
| **社交功能表** | 部分 | 读写 | 管理 | 作品展示、社交互动 |
## 🤖 AI平台服务集成规范

### 🎯 AI平台服务集成机制
**详细配置**: 参见 `dev-aiapi-guidelines.mdc`

#### 支持的AI平台
**通过 `php\aiapi` 集成的第三方AI平台虚拟服务**

| AI平台 | 主要功能 | 开发环境 | 生产环境 | 规范文档 |
|--------|---------|----------|----------|----------|
| **DeepSeek** | 文本生成、对话 | 模拟服务 | 真实DeepSeek API | `ai-api-deepseek.com-guidelines.mdc` |
| **LiblibAI** | 图像生成 | 模拟服务 | 真实LiblibAI API | `ai-api-liblib.art-guidelines.mdc` |
| **KlingAI** | 视频生成 | 模拟服务 | 真实KlingAI API | `ai-api-klingai.com-guidelines.mdc` |
| **MiniMax** | 多模态AI | 模拟服务 | 真实MiniMax API | `ai-api-minimaxi.com-guidelines.mdc` |
| **火山引擎豆包** | 语音合成、音效 | 模拟服务 | 真实火山引擎API | `ai-api-volcengine.com-guidelines.mdc` |

#### 环境切换机制
**开发环境**: 工具API接口服务 → AI服务模拟 → 模拟数据返回
**生产环境**: 工具API接口服务 → 真实AI平台 → 真实数据返回
**切换方式**: 通过 `AI_SERVICE_MODE` 环境变量控制（mock/real）

#### AI服务模拟组件 (@php/aiapi/)
**核心职责**: 开发环境AI服务模拟（临时组件）
- **模拟响应**: 模拟5个AI平台的API响应，保持100%兼容性
- **开发支持**: 支持本地开发和测试，避免真实费用产生
- **支持平台**: DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包
- **环境切换**: 通过 `AI_SERVICE_MODE` 环境变量控制切换
- **生产环境**: 上线后通过配置直连真实AI平台，无需修改代码
- **服务地址**: `https://aiapi.tiptop.cn/`（开发环境专用）
- **开发规范**: `dev-aiapi-guidelines.mdc`

**🚨 重要特性**：
- **无缝切换**: 开发环境使用模拟服务，生产环境自动切换到真实服务
- **费用控制**: 开发阶段完全无费用产生，避免误操作成本
- **完全兼容**: 模拟服务API与真实AI平台保持100%一致
- **安全隔离**: 开发数据与生产数据完全隔离

#### 环境切换核心价值
- ✅ **开发效率**: 本地开发无需依赖真实AI平台
- ✅ **成本控制**: 避免开发阶段产生真实AI调用费用
- ✅ **测试完整性**: 可以模拟各种边界情况和异常状态
- ✅ **完全兼容**: 确保与真实AI平台API的100%兼容性
- ✅ **架构纯净**: 工具API接口服务保持业务逻辑纯净，无模拟污染
- ✅ **安全隔离**: 模拟环境与真实环境完全隔离，无数据泄露风险

### AI业务场景配置
**图像生成业务**: LiblibAI（主） + MiniMax（备选）
**视频生成业务**: KlingAI（主） + MiniMax（备选）
**文本生成业务**: DeepSeek（主） + MiniMax（备选）
**语音处理业务**: 火山引擎豆包（主） + MiniMax（备选）

### 🔗 第三方服务集成规范

#### 支持的第三方服务平台
**详细配置**: 参见 `dev-thirdapi-guidelines.mdc`

| 第三方服务 | 主要功能 | 开发环境 | 生产环境 |
|-----------|---------|----------|----------|
| **微信服务** | OAuth登录、微信支付 | 模拟服务 | 真实微信API |
| **支付宝支付** | 统一收单、交易查询、退款 | 模拟服务 | 真实支付宝API |
| **短信服务** | 阿里云短信、腾讯云短信 | 模拟服务 | 真实短信API |
| **邮件服务** | SMTP发送、SendCloud | 模拟服务 | 真实邮件API |

#### 环境切换机制
**开发环境**: 工具API接口服务 → 第三方服务模拟 → 模拟数据返回
**生产环境**: 工具API接口服务 → 真实第三方平台 → 真实数据返回
**切换方式**: 通过 `THIRD_PARTY_MODE` 环境变量控制（mock/real）

#### 第三方服务模拟组件 (@php/thirdapi/)
**核心职责**: 开发环境第三方服务模拟（临时组件）
- **模拟响应**: 模拟第三方服务API响应
- **开发支持**: 支持本地开发和测试
- **生产环境**: 上线后直连真实第三方服务
- **服务地址**: `https://thirdapi.tiptop.cn/`
- **开发规范**: `dev-thirdapi-guidelines.mdc`

#### 环境切换核心价值
- ✅ **开发效率**: 本地开发无需依赖真实第三方平台
- ✅ **成本控制**: 避免开发阶段产生真实费用
- ✅ **测试完整性**: 可以模拟各种边界情况和异常状态
- ✅ **完全兼容**: 确保与真实第三方平台API的100%兼容性
- ✅ **架构纯净**: 工具API接口服务保持业务逻辑纯净，无模拟污染
- ✅ **安全隔离**: 模拟环境与真实环境完全隔离，无数据泄露风险
## 🔐 认证与安全机制

### Token认证机制
**工具API接口服务**使用统一的AuthService认证机制：

**支持的认证方式**：
- **Header认证**: `Authorization: Bearer {token}`
- **Query参数**: `?token={token}`

**Token特性**：
- **存储方式**: Redis存储，格式 `user:token:{user_id}`
- **加密存储**: 使用ApiTokenHelper::encryptToken()加密
- **有效期**: 30-35天随机TTL
- **验证机制**: 支持Token失效检查和用户信息验证

### 安全架构特性
- **密钥安全传输**: 不持久化存储
- **权限二次验证**: 关键操作需要二次确认
- **数据隔离**: 发布资源与用户创作资源完全隔离
- **地址保护**: 发布资源地址不暴露给用户
## 🚨 关键架构原则

### 资源下载架构铁律
**核心原则**：
1. **资源下载铁律**: 所有用户创作资源（视频、风格、角色、音乐、音效等）必须由Py视频创作工具直接从AI平台下载到本地
2. **服务器职责边界**: API服务器只负责管理资源的URL、状态、元数据等信息，绝不进行资源文件的中转下载
3. **禁止事项**: 严禁在API服务器上进行任何形式的资源文件生成、处理、存储、中转下载

### WebSocket使用边界
1. **仅Py视频创作工具使用**: AI生成进度推送、任务状态通知
2. **WEB工具禁用**: 避免不必要的连接和资源消耗
3. **安全传输**: 密钥加密传输，不持久化存储

### AI平台服务集成边界
1. **环境切换铁律**: 开发环境使用 `@php/aiapi/` 模拟服务，生产环境使用真实AI平台
2. **配置驱动切换**: 通过 `AI_SERVICE_MODE` 环境变量控制，禁止硬编码
3. **统一调用接口**: 必须使用 `AiServiceClient::callWithUserChoice()` 统一调用
4. **100%兼容要求**: 模拟服务API必须与真实AI平台保持完全一致
5. **费用控制**: 开发阶段严禁产生真实AI调用费用，所有调用走模拟服务
6. **第三方AI平台集成**: 严格按照第三方AI平台API接口文档进行集成

### 第三方服务集成边界
1. **环境切换铁律**: 开发环境使用 `@php/thirdapi/` 模拟服务，生产环境使用真实服务
2. **配置驱动切换**: 通过 `THIRD_PARTY_MODE` 环境变量控制，禁止硬编码
3. **统一调用接口**: 必须使用 `ThirdPartyServiceClient::call()` 统一调用
4. **100%兼容要求**: 模拟服务API必须与真实服务保持完全一致
5. **费用控制**: 开发阶段严禁产生真实费用，所有调用走模拟服务

### 性能优化策略
- **并发支持**: 设计支持1000用户同时使用
- **缓存策略**: MySQL主存储 + Redis缓存层
- **超时管理**: 图像5分钟、视频30分钟、文本1分钟、语音2分钟
## 🎨 作品发布规则

### 可发布作品类型
1. **风格作品**: 用户创建的剧情风格可发布到风格广场
2. **角色作品**: 用户创建的角色可发布到角色广场
3. **视频作品**: 用户创作完成的视频可发布到作品广场

### 发布安全机制
1. **资源隔离**: 发布资源与用户创作资源完全隔离
2. **地址保护**: 发布资源地址不暴露给用户
3. **权限控制**: 仅审核通过的作品可在广场展示
4. **版权保护**: 发布资源受系统版权保护机制管理
## 📊 数据库设计概述

**完整数据库设计规范**: 详见 **[dev-api-guidelines-database.mdc](.cursor/rules/dev-api-guidelines-database.mdc)**

### 核心数据表
**用户管理表**: `p_users`、`p_user_profiles`、`p_user_tokens`
**项目管理表**: `p_projects`、`p_project_scenes`、`p_project_resources`
**AI任务表**: `p_ai_generation_tasks`、`p_ai_generation_results`
**素材库表**: `p_style_library`、`p_character_library`、`p_voice_library`
**作品发布表**: `p_published_works`、`p_work_categories`
**社交功能表**: `p_likes`、`p_comments`、`p_work_interactions`
**积分系统表**: `p_user_points`、`p_point_transactions`
**代理系统表**: `p_agents`、`p_agent_commissions`

### 数据库特性
- **字段类型规范**: 详细的字段类型、长度、约束、默认值定义
- **索引优化**: 高频查询字段、复合索引、唯一索引策略
- **关系设计**: 5条核心关系链和外键约束规范
- **性能优化**: 数据一致性约束和性能建议
## ⚡ 性能期望与技术要求

### 系统性能指标
- **响应延迟**: ≤30000ms（30秒）
- **并发支持**: 1000用户同时使用
- **系统可用性**: 99.9%
- **数据一致性**: 强一致性要求

### 环境模式对比
| 环境模式 | 响应时间 | 费用产生 | 数据真实性 | 适用场景 |
|---------|---------|---------|-----------|---------|
| **mock模式** | 100-500ms | 无费用 | 模拟数据 | 开发测试 |
| **real模式** | 1-30秒 | 真实费用 | 真实数据 | 生产环境 |

## 📚 开发文档使用指南

### 🎯 知识及规范文档选择决策树

**针对不同开发场景，AI程序员必须严格按照以下决策树逻辑选择文档：**

```
开发任务分析
├─ 是否需要理解架构/业务流程？
│  ├─ 系统架构理解 → `dev-chart-guidelines.mdc` (架构图表) + `index-new.mdc` (架构规范)
│  ├─ 业务流程理解 → `dev-chart-guidelines.mdc` (流程图表) + 对应的API文档
│  ├─ 组件职责边界 → `dev-chart-guidelines.mdc` (职责矩阵) + `index-new.mdc` (详细规范)
│  ├─ 新人入门学习 → `dev-chart-guidelines.mdc` (图表概览) → `index-new.mdc` (完整规范)
│  └─ 否 → 继续判断
├─ 是否涉及数据库操作？
│  ├─ 数据表设计/修改 → `dev-api-guidelines-database.mdc` (必须主文档) + `dev-api-guidelines-add.mdc` (新增规范)
│  │  ├─ 新增数据表 → 参考其中表的设计规范和字段类型标准
│  │  ├─ 修改表结构 → 遵循字段类型规范和约束条件
│  │  └─ 表关系设计 → 参考5条核心关系链和外键约束规范
│  ├─ 数据库迁移 → `dev-api-guidelines-database.mdc` (字段类型规范) + `dev-api-guidelines-edit.mdc` (修改规范)
│  │  ├─ 创建迁移文件 → 使用详细的字段类型和长度限制定义
│  │  ├─ 修改字段类型 → 遵循DECIMAL、VARCHAR、ENUM等类型规范
│  │  └─ 添加索引约束 → 参考索引建议和性能优化策略
│  ├─ 模型关系设计 → `dev-api-guidelines-database.mdc` (关系说明) + 对应的API文档
│  │  ├─ 外键关联 → 参考数据表关系说明和关联规范
│  │  ├─ 多态关联 → 参考通用设计模式（如p_likes、p_comments表）
│  │  └─ 业务关联 → 参考核心关系链设计
│  ├─ 数据库性能优化 → `dev-api-guidelines-database.mdc` (索引建议) + `dev-api-guidelines-edit.mdc` (优化规范)
│  │  ├─ 索引优化 → 参考高频查询字段、复合索引、唯一索引策略
│  │  ├─ 查询优化 → 参考数据一致性约束和性能建议
│  │  └─ 存储优化 → 参考字段类型选择和存储策略
│  └─ 否 → 继续判断
├─ 是否涉及环境切换？
│  ├─ AI服务环境切换 → `dev-aiapi-guidelines.mdc` (主) + `index-new.mdc` (架构参考)
│  ├─ 第三方服务环境切换 → `dev-thirdapi-guidelines.mdc` (主) + `index-new.mdc` (架构参考)
│  └─ 否 → 继续判断
├─ 是否涉及客户端对接？
│  ├─ Py视频创作工具对接 → `dev-api-guidelines-pyapi.mdc` (最高权重) + `dev-chart-guidelines.mdc` (业务流程)
│  ├─ WEB工具对接 → `dev-api-guidelines-webapi.mdc` (最高权重) + `dev-chart-guidelines.mdc` (业务流程)
│  ├─ 管理后台对接 → `dev-api-guidelines-adminapi.mdc` (最高权重) + `dev-chart-guidelines.mdc` (业务流程)
│  └─ 否 → 继续判断
├─ 是否涉及集成AI功能？
│  ├─ 是 → `dev-aiapi-guidelines.mdc` (必须主文档)
│  │  ├─ 新增集成AI功能 → + `dev-api-guidelines-add.mdc` (新增规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  ├─ 修复集成AI功能 → + `dev-api-guidelines-edit.mdc` (修复规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  ├─ AI平台服务集成 → 仅使用 `dev-aiapi-guidelines.mdc`
│  │  └─ DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包集成 → 仅使用 `dev-aiapi-guidelines.mdc`
│  └─ 否 → 继续判断
├─ 是否涉及第三方服务？
│  ├─ 是 → `dev-thirdapi-guidelines.mdc` (必须主文档)
│  │  ├─ 新增第三方功能 → + `dev-api-guidelines-add.mdc` (新增规范)
│  │  ├─ 修复第三方功能 → + `dev-api-guidelines-edit.mdc` (修复规范)
│  │  ├─ 第三方服务集成 → 仅使用 `dev-thirdapi-guidelines.mdc`
│  │  └─ 微信/支付宝/短信/邮件集成 → 仅使用 `dev-thirdapi-guidelines.mdc`
│  └─ 否 → 继续判断
├─ 是新功能开发？
│  ├─ 是 → `dev-api-guidelines-add.mdc` (主文档)
│  │  ├─ 涉及集成AI功能 → + `dev-aiapi-guidelines.mdc` (AI专项规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  ├─ 涉及第三方服务 → + `dev-thirdapi-guidelines.mdc` (第三方专项规范)
│  │  ├─ 涉及数据库设计 → + `dev-api-guidelines-database.mdc` (数据库规范)
│  │  └─ 涉及客户端对接 → + 对应的 `dev-api-guidelines-pyapi.mdc 或 dev-api-guidelines-webapi.mdc 或 dev-api-guidelines-adminapi.mdc` + `dev-chart-guidelines.mdc` (业务流程)
│  └─ 否 → 继续判断
├─ 是问题修复？
│  ├─ 是 → `dev-api-guidelines-edit.mdc` (主文档)
│  │  ├─ 集成AI相关问题 → + `dev-aiapi-guidelines.mdc` (AI专项规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  ├─ 第三方服务问题 → + `dev-thirdapi-guidelines.mdc` (第三方专项规范)
│  │  ├─ 数据库相关问题 → + `dev-api-guidelines-database.mdc` (数据库规范)
│  │  └─ 客户端对接问题 → + 对应的 `dev-api-guidelines-pyapi.mdc 或 dev-api-guidelines-webapi.mdc 或 dev-api-guidelines-adminapi.mdc` + `dev-chart-guidelines.mdc` (业务流程)
│  └─ 否 → 继续判断
└─ 复杂场景 → 多文档组合使用
   ├─ 架构重构 → `dev-chart-guidelines.mdc` (架构图) + `dev-api-guidelines-edit.mdc` + `dev-api-guidelines-add.mdc`
   ├─ 数据库重构 → `dev-api-guidelines-database.mdc` (数据库设计) + `dev-api-guidelines-edit.mdc` (迁移规范)
   ├─ 性能优化 → `dev-chart-guidelines.mdc` (性能流程) + `dev-api-guidelines-database.mdc` (索引优化) + `dev-api-guidelines-edit.mdc`
   ├─ 安全加固 → `dev-api-guidelines-edit.mdc` + `dev-api-guidelines-add.mdc` (如需新增安全功能)
   ├─ 全栈开发 → `dev-chart-guidelines.mdc` (完整架构) + `dev-api-guidelines-database.mdc` (数据设计) + 对应的多个文档组合
   └─ AI程序员规范化 → `dev-chart-guidelines.mdc` (行为规范指南) + `index-new.mdc` (详细规范)
```

### 📋 快速参考指南

**常用场景快速索引：**

#### 📊 架构理解与业务流程
- **系统架构理解** → `dev-chart-guidelines.mdc` + `index-new.mdc`
- **业务流程理解** → `dev-chart-guidelines.mdc` + 对应API文档
- **组件职责边界** → `dev-chart-guidelines.mdc` + `index-new.mdc`

#### 🗄️ 数据库相关开发
- **数据表设计/修改** → `dev-api-guidelines-database.mdc` (必须主文档)
- **数据库迁移** → `dev-api-guidelines-database.mdc` + `dev-api-guidelines-edit.mdc`
- **模型关系设计** → `dev-api-guidelines-database.mdc` + 对应API文档

#### 🤖 集成AI功能开发
- **新增集成AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-add.mdc`
- **修复集成AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
- **AI平台服务集成** → `dev-aiapi-guidelines.mdc`（主文档）


#### 📊 集成第三方服务开发
- **新增集成第三方功能** → `dev-thirdapi-guidelines.mdc` + `dev-api-guidelines-add.mdc`
- **修复集成第三方功能** → `dev-thirdapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
- **第三方服务集成** → `dev-thirdapi-guidelines.mdc`（主文档）
---

## 📝 技术栈总结

### 后端技术栈
- **工具API接口服务**: Lumen 10 + PHP 8.1.29 + MySQL 8.0 + Redis 7.4.2
- **AI服务集成模拟**: Lumen 10 + PHP 8.1.29（开发环境专用）
- **第三方服务集成模拟**: Lumen 10 + PHP 8.1.29（开发环境专用）
- **数据库**: MySQL 8.0.12
- **缓存**: Redis 7.4.2
- **Web服务器**: Nginx 1.26.2

### 前端技术栈
- **管理后台**: Vue 3 + Element Plus + TypeScript + Vite + Nginx（前后端分离）
- **WEB网页工具**: Vue 3 + Element Plus + TypeScript + Vite + Nginx（前后端分离，响应式设计）
- **Py视频创作工具**: Python 3.12 + PySide6 + PyInstaller + WebSocket客户端

### 客户端技术栈
- **Py视频创作工具**: Python 3.12 + PySide6 + PyInstaller
- **支持平台**: Windows 10/11、macOS
- **打包方式**: PyInstaller可执行文件
- **通信协议**: HTTP API + WebSocket

### 开发工具与环境
- **开发环境**: Windows 11
- **生产环境**: CentOS 8 Stream
- **版本控制**: Git
- **API文档**: 基于OpenAPI规范
- **包管理器**: npm/pnpm（前端）、pip（Python）、Composer（PHP）

---

## 📝 文档维护说明

本文档是AI视频创作工具系统的核心架构规范，所有开发工作都应严格遵循本文档的规定。如需修改架构设计，必须先更新本文档并经过团队评审。

**最后更新**: 2025-08-05
**文档版本**: v4.15 - 文档结构优化版
**维护人员**: 开发团队

### v4.15 更新内容
- ✅ **删除重复节点**: 移除过时的"技术栈与环境配置"节点，避免信息重复
- ✅ **文档结构优化**: 统一使用后面更完整的"技术栈总结"节点
- ✅ **信息一致性**: 确保技术栈信息的唯一性和准确性
- ✅ **文档精简**: 提高文档可读性，减少冗余内容
- ✅ **维护效率**: 降低文档维护成本，避免多处更新

### v4.14 更新内容
- ✅ **前端部署信息完善**: 为管理后台和WEB网页工具添加完整的部署信息
- ✅ **Web服务器统一**: 明确前端应用都使用Nginx 1.26.2进行静态文件服务
- ✅ **构建工具说明**: 补充Vite构建工具信息，用于开发和生产构建
- ✅ **部署架构清晰**: 前端静态部署 + 后端API服务的架构更加明确
- ✅ **技术栈一致性**: 确保所有前端组件的部署方式统一

### v4.13 更新内容
- ✅ **平台支持优化**: Py视频创作工具支持平台调整为Windows 10/11、macOS
- ✅ **技术可行性**: 避免Windows老版本兼容性问题，专注主流平台
- ✅ **开发复杂度控制**: 降低多版本兼容性带来的开发和测试成本
- ✅ **性能保障**: 确保在主流Windows版本上的最佳性能表现
- ✅ **文档一致性**: 统一更新所有相关的平台支持说明

### v4.12 更新内容
- ✅ **完整技术栈信息**: 为所有6个核心组件添加详细的技术栈信息
- ✅ **工具API接口服务**: 补充Lumen 10 + PHP 8.1.29 + MySQL + Redis技术栈
- ✅ **Py视频创作工具**: 补充Python 3.12 + PySide6 + PyInstaller客户端技术栈
- ✅ **AI服务集成模拟**: 补充Lumen 10轻量级API服务技术栈
- ✅ **第三方服务集成模拟**: 补充Lumen 10轻量级API服务技术栈
- ✅ **技术栈总结优化**: 按后端、前端、客户端分类，提供完整的技术栈概览

### v4.11 更新内容
- ✅ **WEB网页工具技术栈统一**: 采用与管理后台相同的Vue 3 + Element Plus技术栈
- ✅ **前端架构一致性**: 管理后台和WEB网页工具都采用前后端分离架构
- ✅ **开发规范统一**: WEB网页工具使用相同的Vue 3开发规范
- ✅ **技术栈简化**: 前端统一使用Vue 3 + Element Plus，降低学习成本
- ✅ **响应式设计**: WEB网页工具支持PC端、移动端自适应

### v4.10 更新内容
- ✅ **移除Laravel依赖**: 删除`dev-backend-guidelines.mdc`和`dev-laravel10-guidelines.mdc`
- ✅ **新增Vue 3规范**: 添加`dev-vue3-guidelines.mdc`管理后台前端开发规范
- ✅ **AdminApi开发规范**: 更新为Lumen 10规范，移除Laravel依赖
- ✅ **文档结构优化**: 精简开发规范文档，提高AI开发效率
- ✅ **技术栈一致性**: 确保所有组件的开发规范与实际技术栈一致

### v4.9 更新内容
- ✅ **管理后台技术栈优化**: 从Laravel 10改为Vue 3 + Element Plus前后端分离架构
- ✅ **AI开发友好**: 降低技术栈复杂度，提高AI程序员开发效率
- ✅ **前后端分离**: 管理后台通过AdminApi接口实现完全的前后端分离
- ✅ **部署简化**: 管理后台前端静态部署，无需PHP环境
- ✅ **技术栈统一**: 减少技术栈种类，降低学习和维护成本
- ✅ **开发规范更新**: 更新为Vue 3开发规范，移除Laravel依赖

### v4.8 更新内容
- ✅ **补充API文档规范**: 为所有组件添加对应的API文档开发规范
- ✅ **工具API接口服务**: 明确PyApi、WebApi、AdminApi三大分类的API文档规范
- ✅ **AI服务集成模拟**: 补充AI平台API文档规范（包含5个AI平台）
- ✅ **第三方服务集成模拟**: 补充第三方服务API文档规范（包含微信、支付宝、短信、邮件）
- ✅ **管理后台和WEB工具**: 明确对应的API文档规范关联
- ✅ **项目目录结构**: 更新项目目录结构，补充缺失的API文档规范文件

### v4.7 更新内容
- ✅ **新增共用数据表规范**: 详细说明PyApi、WebApi、AdminApi三大分类的数据表共享机制
- ✅ **数据访问权限规范**: 明确每个API分类对不同数据表的访问权限（完全访问/只读访问/禁止访问）
- ✅ **数据一致性保障**: 建立跨API分类的事务管理和数据同步机制
- ✅ **数据表使用矩阵**: 提供清晰的数据表使用权限对照表
- ✅ **数据表分类说明**: 按业务功能对49个数据表进行分类和权限说明

### v4.6 更新内容
- ✅ **完善API接口分类**: 明确PyApi、WebApi、AdminApi三大接口分类
- ✅ **详细职责说明**: 为每个API分类添加控制器目录、业务层目录、开发规范
- ✅ **功能边界明确**: 详细说明每个API分类的核心功能和架构边界
- ✅ **开发规范关联**: 明确每个API分类对应的开发规范文档
- ✅ **职责分工清晰**: 区分AI任务调度、展示社交、系统管理三大职责

### v4.5 更新内容
- ✅ **恢复知识及规范文档选择决策树**: 恢复完整的决策树逻辑，更适合AI程序员使用
- ✅ **决策树逻辑优化**: 采用if-else结构，提供清晰的文档选择路径
- ✅ **复杂场景处理**: 包含架构重构、数据库重构、性能优化等复杂场景的文档组合规则
- ✅ **快速参考指南**: 保留简化版本作为常用场景的快速索引
- ✅ **AI友好设计**: 决策树结构更符合AI程序的逻辑思维模式

### v4.4 更新内容
- ✅ **补充AI平台服务集成规范**: 明确 `@php/aiapi/` 集成第三方AI平台的虚拟服务机制
- ✅ **AI服务环境切换完善**: 详细说明AI平台服务的环境切换流程和架构边界
- ✅ **第三方AI平台集成说明**: 明确DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包的集成方式
- ✅ **双重环境切换机制**: AI服务和第三方服务的完整环境切换规范
- ✅ **架构边界强化**: 分别明确AI平台服务和第三方服务的集成边界