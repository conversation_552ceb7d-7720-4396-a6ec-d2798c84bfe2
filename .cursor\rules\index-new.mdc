---
description: AI视频创作工具系统架构规范
globs:
alwaysApply: true
---

# AI视频创作工具系统架构规范

## 📋 项目概述

### 🎯 项目定位
本项目是一个完整的AI视频创作工具生态系统，包含五大核心组件：
- **Py视频创作工具**：客户端视频创作应用
- **WEB网页工具**：展示和用户中心功能
- **管理后台**：系统管理和内容审核
- **工具API接口服务**：统一API接口服务
- **AI服务集成模拟**：开发环境AI服务模拟

**📁 文档路径说明**: 本文档中提到的所有开发规范文档都位于 `@.cursor/rules/` 目录下。

### 🏗️ 系统架构设计原则
- **职责边界清晰**：每个组件职责明确，避免功能重叠
- **服务解耦**：组件间通过标准API接口通信，降低耦合度
- **资源本地化**：用户创作资源由Py视频创作工具直接从AI平台下载到本地
- **可选发布机制**：作品发布为增值服务，用户可选择是否发布到广场
- **环境切换机制**：支持开发环境（mock）和生产环境（real）无缝切换

## 🔧 技术栈与环境配置

### 技术栈总览
**后端技术栈**：
- **管理后台**: Laravel 10 + PHP 8.1.29 + MySQL 8.0 + Redis
- **工具API接口服务**: Lumen 10 + PHP 8.1.29 + MySQL 8.0 + Redis
- **AI服务模拟**: PHP 8.1.29（开发环境专用）

**前端技术栈**：
- **WEB网页工具**: HTML5 + CSS3 + JavaScript（静态部署）
- **Py视频创作工具**: Python 3.12（桌面客户端）

**基础设施**：
- **Web服务器**: Nginx 1.26.2
- **数据库**: MySQL 8.0.12
- **缓存**: Redis 7.4.2
- **版本控制**: Git
- **API文档**: 基于OpenAPI规范

### 环境配置
**开发环境**（Windows 11）：
- Python 3.12 + Nginx 1.26.2 + PHP 8.1.29 + MySQL 8.0.12 + Redis 7.4.2

**生产环境**（CentOS 8 Stream）：
- **管理后台**: Nginx + Laravel 10 + MySQL + Redis
- **工具API接口服务**: Nginx + Lumen 10 + MySQL + Redis
- **WEB网页工具**: Nginx（静态部署）
- **Py视频创作工具**: Windows/Mac 客户端
- **AI服务**: 直连真实AI平台（无需模拟服务）

## 📁 项目目录结构

```
项目根目录/
├── php/                  # PHP 后端服务
│   ├── backend/          # 管理后台（Laravel 10）
│   ├── api/              # 工具API接口服务（Lumen 10）
│   ├── web/              # WEB网页工具（静态前端）
│   ├── aiapi/            # AI服务集成模拟（开发环境）
│   └── thirdapi/         # 第三方服务集成模拟（开发环境）
├── python/               # Py视频创作工具（Python 3.12）
└── .cursor/rules/        # 开发规范文档（19个）
    ├── index-new.mdc                         # 📋 主规范文档
    ├── dev-chart-guidelines.mdc              # 📊 架构图表和业务流程规范
    ├── dev-api-guidelines-database.mdc       # 🗄️ 数据库设计规范
    ├── dev-api-guidelines-add.mdc            # ➕ 新功能开发规范
    ├── dev-api-guidelines-edit.mdc           # 🔧 问题修复和优化规范
    ├── dev-api-guidelines-pyapi.mdc          # 🐍 Py视频创作工具API规范
    ├── dev-backend-guidelines.mdc            # 🏢 管理后台开发规范
    ├── dev-aiapi-guidelines.mdc              # 🤖 AI服务集成开发规范
    ├── dev-thirdapi-guidelines.mdc           # 🔗 第三方服务集成规范
    ├── dev-laravel10-guidelines.mdc          # 🎨 Laravel 10 开发规范
    ├── dev-lumen10-guidelines.mdc            # ⚡ Lumen 10 开发规范
    ├── dev-php8.1.29-guidelines.mdc          # 🐘 PHP 8.1.29 开发规范
    ├── dev-python3.12-guidelines.mdc         # 🐍 Python 3.12 开发规范
    ├── dev-mysql8.0-guidelines.mdc           # 🗄️ MySQL 8.0 开发规范
    ├── ai-api-deepseek.com-guidelines.mdc    # 🧠 DeepSeek AI API 规范
    ├── ai-api-liblib.art-guidelines.mdc      # 🎨 LiblibAI API 规范
    ├── ai-api-klingai.com-guidelines.mdc     # 🎬 KlingAI API 规范
    ├── ai-api-minimaxi.com-guidelines.mdc    # 🔮 MiniMax AI API 规范
    └── ai-api-volcengine.com-guidelines.mdc  # 🌋 火山引擎豆包 API 规范
```

## 🎯 核心组件职责定义

### 1. 🐍 Py视频创作工具 (@python/)
**核心职责**：AI视频创作的主要客户端应用
- **用户界面**：提供完整的视频创作操作界面
- **AI集成**：调用工具API接口服务进行AI内容生成
- **资源管理**：直接从AI平台下载资源到本地
- **视频编辑**：本地视频编辑和导出功能
- **开发规范**：`dev-api-guidelines-pyapi.mdc` + `dev-python3.12-guidelines.mdc`

### 2. ⚡ 工具API接口服务 (@php/api/)
**核心职责**：统一的后端API接口服务
- **业务逻辑**：处理所有业务逻辑和数据管理
- **AI服务调用**：统一调用各AI平台服务
- **用户认证**：Token认证和权限管理
- **数据存储**：MySQL数据存储和Redis缓存
- **开发规范**：`dev-api-guidelines-add.mdc` + `dev-lumen10-guidelines.mdc`

### 3. 🏢 管理后台 (@php/backend/)
**核心职责**：系统管理和内容审核
- **用户管理**：用户账户和权限管理
- **内容审核**：作品发布审核和管理
- **系统监控**：系统状态和性能监控
- **数据统计**：业务数据分析和报表
- **开发规范**：`dev-backend-guidelines.mdc` + `dev-laravel10-guidelines.mdc`

### 4. 🌐 WEB网页工具 (@php/web/)
**核心职责**：用户展示和社区功能
- **作品展示**：作品广场和用户作品展示
- **用户中心**：用户信息管理和设置
- **社交功能**：点赞、评论、分享等互动
- **静态部署**：纯前端静态页面部署

### 5. 🤖 AI服务集成模拟 (@php/aiapi/)
**核心职责**：开发环境AI服务模拟（临时组件）
- **模拟响应**：模拟5个AI平台的API响应
- **开发支持**：支持本地开发和测试
- **环境切换**：支持mock/real模式切换
- **生产环境**：上线后直连真实AI平台
- **支持平台**：DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包
- **开发规范**：`dev-aiapi-guidelines.mdc`

### 6. 🔗 第三方服务集成模拟 (@php/thirdapi/)
**核心职责**：开发环境第三方服务模拟（临时组件）
- **模拟响应**：模拟第三方服务API响应
- **开发支持**：支持本地开发和测试
- **生产环境**：上线后直连真实第三方服务
- **开发规范**：`dev-thirdapi-guidelines.mdc`

## 📊 系统架构与业务流程

### 🏗️ 完整系统架构
**详细架构图**: 参见 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)**

**架构层次结构**：
- **用户层**: Py视频创作工具、WEB网页工具、管理后台
- **业务服务层**: 工具API接口服务、WebSocket服务、AI资源管理服务
- **环境切换层**: AiServiceClient、ThirdPartyServiceClient
- **开发支持层**: AI服务模拟、第三方服务模拟（开发环境专用）
- **数据存储层**: MySQL数据库、Redis缓存
- **外部服务层**: 5个AI平台、第三方服务

### 🔄 业务流程图集合
**详细流程图**: 参见 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)**

**包含流程**：
- **Py视频创作工具**: 用户管理、业务功能、项目管理、AI核心流程
- **WEB网页工具**: 用户中心、作品展示、社交互动
- **管理后台**: 用户管理、内容审核、系统监控
- **环境切换机制**: mock/real模式切换流程

### 🎯 关键架构特性
1. **职责边界清晰**: 每个组件职责明确，避免功能重叠
2. **环境切换机制**: 开发/生产环境无缝切换
3. **避免循环依赖**: 异步事件驱动架构
4. **WebSocket边界**: 仅Py视频创作工具使用实时通信
5. **性能优化**: 支持1000并发用户，MySQL+Redis双重保障
6. **安全架构**: 密钥安全传输，权限二次验证
7. **资源本地化**: 用户资源直接从AI平台下载到本地

## 🔄 环境切换机制

### 🎯 环境切换核心价值
**详细机制图**: 参见 **[dev-chart-guidelines.mdc](.cursor/rules/dev-chart-guidelines.mdc)**

**关键优势**：
- ✅ **开发效率**: 本地开发无需依赖真实AI平台
- ✅ **成本控制**: 避免开发阶段产生真实费用
- ✅ **测试完整性**: 模拟各种边界情况和异常状态
- ✅ **完全兼容**: 确保与真实AI平台API的100%兼容性
- ✅ **架构纯净**: 工具API接口服务保持业务逻辑纯净
- ✅ **安全隔离**: 模拟环境与真实环境完全隔离

### 🔀 切换机制说明
- **开发环境**: 工具API → AI服务模拟 → 模拟数据返回
- **生产环境**: 工具API → 真实AI平台 → 真实数据返回
- **切换方式**: 通过配置自动路由到不同的服务端点

## 🎯 API接口服务详细职责

### 📱 Py视频创作工具API (@php/api/app/Http/Controllers/PyApi)
**核心功能**：
- **AI任务调度**: 文生文、图生图、图生视频、语音生成、音效音乐生成
- **用户管理**: 注册登录、资料修改、密码找回、用户认证
- **积分系统**: 充值积分、积分管理、积分明细
- **代理系统**: 代理推广、代理结算
- **作品管理**: 作品发布、数据处理
- **实时通信**: WebSocket AI生成进度推送（仅Py工具使用）

**架构边界**：
- ❌ 不储存且不中转用户创作过程中AI生成的资源
- ❌ 不处理视频编辑、客户端UI逻辑、本地文件操作

### 🌐 WEB网页工具API (@php/api/app/Http/Controllers/WebApi)
**核心功能**：
- **展示服务**: 功能介绍、价格方案查询
- **作品广场**: 作品数据查询、分类筛选、搜索查看、作品详情展示
- **用户服务**: 注册登录、资料修改、密码找回、用户认证
- **积分系统**: 充值积分、积分管理、积分明细
- **代理系统**: 代理推广、代理结算
- **响应式设计**: 支持PC端、移动端、Py工具嵌入（1200px/800px窗口）

**架构边界**：
- ❌ 不包含视频创作功能、AI生成功能
### 🏢 管理后台API (@php/api/app/Http/Controllers/AdminApi)
**核心功能**：
- **用户管理**: 用户账户管理、权限控制
- **内容审核**: 作品发布审核、内容管理
- **系统监控**: 系统状态监控、性能分析
- **数据统计**: 业务数据分析、报表生成
- **系统配置**: 平台参数配置、功能开关

**架构边界**：
- ❌ 不包含视频创作功能、AI生成功能
## 🤖 AI平台与服务配置

### 支持的AI平台
**详细配置**: 参见各AI平台专门的规范文档

| AI平台 | 主要功能 | 规范文档 |
|--------|---------|----------|
| **DeepSeek** | 文本生成、对话 | `ai-api-deepseek.com-guidelines.mdc` |
| **LiblibAI** | 图像生成 | `ai-api-liblib.art-guidelines.mdc` |
| **KlingAI** | 视频生成 | `ai-api-klingai.com-guidelines.mdc` |
| **MiniMax** | 多模态AI | `ai-api-minimaxi.com-guidelines.mdc` |
| **火山引擎豆包** | 语音合成、音效 | `ai-api-volcengine.com-guidelines.mdc` |

### AI业务场景配置
**图像生成业务**: LiblibAI（主） + MiniMax（备选）
**视频生成业务**: KlingAI（主） + MiniMax（备选）
**文本生成业务**: DeepSeek（主） + MiniMax（备选）
**语音处理业务**: 火山引擎豆包（主） + MiniMax（备选）
## 🔐 认证与安全机制

### Token认证机制
**工具API接口服务**使用统一的AuthService认证机制：

**支持的认证方式**：
- **Header认证**: `Authorization: Bearer {token}`
- **Query参数**: `?token={token}`

**Token特性**：
- **存储方式**: Redis存储，格式 `user:token:{user_id}`
- **加密存储**: 使用ApiTokenHelper::encryptToken()加密
- **有效期**: 30-35天随机TTL
- **验证机制**: 支持Token失效检查和用户信息验证

### 安全架构特性
- **密钥安全传输**: 不持久化存储
- **权限二次验证**: 关键操作需要二次确认
- **数据隔离**: 发布资源与用户创作资源完全隔离
- **地址保护**: 发布资源地址不暴露给用户
## 🚨 关键架构原则

### 资源下载架构铁律
**核心原则**：
1. **资源下载铁律**: 所有用户创作资源（视频、风格、角色、音乐、音效等）必须由Py视频创作工具直接从AI平台下载到本地
2. **服务器职责边界**: API服务器只负责管理资源的URL、状态、元数据等信息，绝不进行资源文件的中转下载
3. **禁止事项**: 严禁在API服务器上进行任何形式的资源文件生成、处理、存储、中转下载

### WebSocket使用边界
1. **仅Py视频创作工具使用**: AI生成进度推送、任务状态通知
2. **WEB工具禁用**: 避免不必要的连接和资源消耗
3. **安全传输**: 密钥加密传输，不持久化存储

### 性能优化策略
- **并发支持**: 设计支持1000用户同时使用
- **缓存策略**: MySQL主存储 + Redis缓存层
- **超时管理**: 图像5分钟、视频30分钟、文本1分钟、语音2分钟
## 🎨 作品发布规则

### 可发布作品类型
1. **风格作品**: 用户创建的剧情风格可发布到风格广场
2. **角色作品**: 用户创建的角色可发布到角色广场
3. **视频作品**: 用户创作完成的视频可发布到作品广场

### 发布安全机制
1. **资源隔离**: 发布资源与用户创作资源完全隔离
2. **地址保护**: 发布资源地址不暴露给用户
3. **权限控制**: 仅审核通过的作品可在广场展示
4. **版权保护**: 发布资源受系统版权保护机制管理
## 📊 数据库设计概述

**完整数据库设计规范**: 详见 **[dev-api-guidelines-database.mdc](.cursor/rules/dev-api-guidelines-database.mdc)**

### 核心数据表
**用户管理表**: `p_users`、`p_user_profiles`、`p_user_tokens`
**项目管理表**: `p_projects`、`p_project_scenes`、`p_project_resources`
**AI任务表**: `p_ai_generation_tasks`、`p_ai_generation_results`
**素材库表**: `p_style_library`、`p_character_library`、`p_voice_library`
**作品发布表**: `p_published_works`、`p_work_categories`
**社交功能表**: `p_likes`、`p_comments`、`p_work_interactions`
**积分系统表**: `p_user_points`、`p_point_transactions`
**代理系统表**: `p_agents`、`p_agent_commissions`

### 数据库特性
- **字段类型规范**: 详细的字段类型、长度、约束、默认值定义
- **索引优化**: 高频查询字段、复合索引、唯一索引策略
- **关系设计**: 5条核心关系链和外键约束规范
- **性能优化**: 数据一致性约束和性能建议
## ⚡ 性能期望与技术要求

### 系统性能指标
- **响应延迟**: ≤30000ms（30秒）
- **并发支持**: 1000用户同时使用
- **系统可用性**: 99.9%
- **数据一致性**: 强一致性要求

### 环境模式对比
| 环境模式 | 响应时间 | 费用产生 | 数据真实性 | 适用场景 |
|---------|---------|---------|-----------|---------|
| **mock模式** | 100-500ms | 无费用 | 模拟数据 | 开发测试 |
| **real模式** | 1-30秒 | 真实费用 | 真实数据 | 生产环境 |

## 📚 开发文档使用指南

### 🎯 文档分类与应用场景

**针对不同开发场景，必须严格按照以下规则使用分类文档：**

#### 📊 架构理解与业务流程
- **系统架构理解** → `dev-chart-guidelines.mdc` (架构图表) + `index-new.mdc` (架构规范)
- **业务流程理解** → `dev-chart-guidelines.mdc` (流程图表) + 对应的API文档
- **组件职责边界** → `dev-chart-guidelines.mdc` (职责矩阵) + `index-new.mdc` (详细规范)

#### 🗄️ 数据库相关开发
- **数据表设计/修改** → `dev-api-guidelines-database.mdc` (必须主文档)
- **数据库迁移** → `dev-api-guidelines-database.mdc` + `dev-api-guidelines-edit.mdc`
- **模型关系设计** → `dev-api-guidelines-database.mdc` + 对应的API文档

#### 🔄 环境切换相关
- **AI服务环境切换** → `dev-aiapi-guidelines.mdc` + `index-new.mdc`
- **第三方服务环境切换** → `dev-thirdapi-guidelines.mdc` + `index-new.mdc`

#### 📱 客户端对接开发
- **Py视频创作工具对接** → `dev-api-guidelines-pyapi.mdc` (最高权重)
- **WEB工具对接** → `dev-api-guidelines-webapi.mdc` (最高权重)
- **管理后台对接** → `dev-api-guidelines-adminapi.mdc` (最高权重)

#### 🤖 AI功能开发
- **新增AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-add.mdc`
- **修复AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`

#### 🔗 第三方服务开发
- **新增第三方功能** → `dev-thirdapi-guidelines.mdc` + `dev-api-guidelines-add.mdc`
- **修复第三方功能** → `dev-thirdapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`

#### ➕ 新功能开发
- **新功能开发** → `dev-api-guidelines-add.mdc` (主文档) + 相关专项文档

#### 🔧 问题修复
- **问题修复** → `dev-api-guidelines-edit.mdc` (主文档) + 相关专项文档
---

## 📝 文档维护说明

本文档是AI视频创作工具系统的核心架构规范，所有开发工作都应严格遵循本文档的规定。如需修改架构设计，必须先更新本文档并经过团队评审。

**最后更新**: 2025-08-05
**文档版本**: v4.2 - 整理优化版
**维护人员**: 开发团队

### v4.2 更新内容
- ✅ **文档结构优化**: 重新组织文档结构，删除重复和冗余内容
- ✅ **信息分类整理**: 按逻辑顺序重新排列，提高可读性
- ✅ **内容去重**: 删除大量重复的技术细节和API接口列表
- ✅ **核心信息突出**: 突出关键架构原则和核心业务流程
- ✅ **文档导航优化**: 简化文档使用指南，明确各文档的应用场景

### v4.1 更新内容
- ✅ **完整文档体系**: 补充所有缺失的开发规范文档
- ✅ **项目目录结构更新**: 包含所有19个规范文档的完整目录结构
- ✅ **新增技术栈文档**: Laravel 10、Lumen 10、PHP 8.1.29、Python 3.12、MySQL 8.0规范
- ✅ **AI平台API规范文档**: DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包API规范
- ✅ **核心业务文档**: 架构图表、新功能开发、问题修复、管理后台开发规范

